import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/ui/card";
import { ChevronRight, ChevronLeft, CheckCircle, AlertCircle } from "lucide-react";
import { Button } from "@/ui/button";
import { useCompanyData } from "@/hooks/use-company-data";
import { useCompany } from "@/contexts/company-context";
import { getStatusClasses } from "@/lib/utils";
import { DataVisualization, generateChartConfig } from "@/components/ui/data-visualization";

interface OrganizationalMaturityProps {
  onNextSection: () => void;
  onPrevSection: () => void;
}

export function OrganizationalMaturity({ onNextSection, onPrevSection }: OrganizationalMaturityProps) {
  const { selectedCompany } = useCompany();
  const { data: contentData, loading: contentLoading } = useCompanyData(selectedCompany, "organizational-maturity");

  if (contentLoading) {
    return <div className="p-6">Loading...</div>;
  }

  if (!contentData) {
    return <div className="p-6">Failed to load organizational maturity data</div>;
  }

  const data = contentData as any; // Type assertion for organizational maturity data
  
  // Prepare radar chart data
  const radarData = [
    { category: "Leadership", verizon: 4, industry: 4.5 },
    { category: "Talent", verizon: 3.5, industry: 4.2 },
    { category: "Culture", verizon: 3.8, industry: 4.0 },
    { category: "Strategy", verizon: 4.2, industry: 4.3 },
    { category: "Execution", verizon: 3.2, industry: 3.8 },
    { category: "Innovation", verizon: 3.6, industry: 4.1 },
  ];

  // Prepare maturity level bar chart data (using data from contentData if available)
  const maturityData = data.maturityLevelAssessment?.categories || [
    { name: "Strategy", verizonLevel: 3.8, industryLevel: 4.2 },
    { name: "People", verizonLevel: 3.5, industryLevel: 4.0 },
    { name: "Process", verizonLevel: 3.2, industryLevel: 3.8 },
    { name: "Technology", verizonLevel: 4.1, industryLevel: 4.3 },
    { name: "Culture", verizonLevel: 3.6, industryLevel: 3.9 },
  ];

  return (
    <div className="p-6 space-y-8">
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-4">Organizational Maturity Assessment</h2>
        <p className="text-gray-700 mb-6">
          {data.description}
        </p>

        {/* Joy Score Radar and Maturity Level Assessment */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Joy Score Radar</CardTitle>
            </CardHeader>
            <CardContent>
              <DataVisualization
                type="radar"
                data={radarData}
                config={generateChartConfig(
                  radarData,
                  ["verizon", "industry"],
                  { 
                    verizon: data.joyScoreRadar?.legend?.verizon || "Verizon",
                    industry: data.joyScoreRadar?.legend?.industryBenchmark || "Industry Benchmark"
                  },
                  { 
                    verizon: "#3b82f6",
                    industry: "#9ca3af"
                  }
                )}
                xKey="category"
                yKey={["verizon", "industry"]}
                height={280}
                tooltip={{ enabled: true }}
                legend={{ enabled: true, position: "bottom" }}
                axes={{
                  yAxis: {
                    domain: [0, 5]
                  }
                }}
              />
              
              {/* Joy Score underneath */}
              <div className="mt-6 bg-amber-50 p-4 rounded-lg">
                <div className="text-left">
                  <div className="text-sm font-bold text-amber-600 mb-2">
                    Joy Score: {data.joyScoreRadar?.overallScore?.value || "4.2"} / {data.joyScoreRadar?.overallScore?.maxValue || "5"} ({data.joyScoreRadar?.overallScore?.description || "Above Average"})
                  </div>
                  <div className="text-xs text-gray-700">
                    {data.joyScoreRadar?.summary || "Overall organizational maturity assessment shows strong performance across key dimensions."}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Maturity Level Assessment</CardTitle>
            </CardHeader>
            <CardContent>
              <DataVisualization
                type="bar"
                data={maturityData}
                config={generateChartConfig(
                  maturityData,
                  ["verizonLevel", "industryLevel"],
                  { 
                    verizonLevel: data.joyScoreRadar?.legend?.verizon || "Verizon",
                    industryLevel: data.joyScoreRadar?.legend?.industryBenchmark || "Industry Benchmark"
                  },
                  { 
                    verizonLevel: "#f87171",
                    industryLevel: "#14b8a6"
                  }
                )}
                xKey="name"
                yKey={["verizonLevel", "industryLevel"]}
                height={384}
                tooltip={{ enabled: true }}
                legend={{ enabled: true, position: "bottom" }}
                axes={{
                  yAxis: {
                    domain: [0, 5]
                  }
                }}
              />
              <div className="mt-2 text-xs text-gray-500 text-center">
                {data.maturityLevelAssessment?.description || "Assessment of organizational maturity across key capability areas."}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Strategic Execution Capability Assessment (SELM) */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>{data.selmAssessment.title}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3 font-medium">Capability</th>
                    <th className="text-left p-3 font-medium">Score</th>
                    <th className="text-left p-3 font-medium">Comment</th>
                  </tr>
                </thead>
                <tbody>
                  {data.selmAssessment.capabilities.map((capability: any, index: number) => (
                    <tr key={index} className="border-b">
                      <td className="p-3 font-medium">{capability.name}</td>
                      <td className="p-3">
                        <span className={`font-semibold ${getStatusClasses(capability.status)}`}>
                          {capability.score} / {capability.maxScore}
                        </span>
                      </td>
                      <td className="p-3 text-sm text-gray-600">
                        {capability.comment}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-gray-700">
                {data.selmAssessment.description}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Organizational Strengths and Improvement Opportunities */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span>Organizational Strengths</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.organizationalStrengths.map((strength: any, index: number) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{strength.title}</h4>
                      <p className="text-sm text-gray-600">
                        {strength.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-amber-600" />
                <span>Improvement Opportunities</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.improvementOpportunities.map((opportunity: any, index: number) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-amber-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{opportunity.title}</h4>
                      <p className="text-sm text-gray-600">
                        {opportunity.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Maturity Enhancement Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle>Maturity Enhancement Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.maturityEnhancementRecommendations.map((recommendation: any, index: number) => (
                <div key={index} className={`border-l-4 pl-4 p-4 rounded-r-lg ${
                  recommendation.priority === 'HIGH' ? 'border-red-500 bg-red-50' : 'border-amber-500 bg-amber-50'
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-gray-900">{recommendation.title}</h4>
                    <span className={`text-xs font-medium px-2 py-1 rounded ${
                      recommendation.priority === 'HIGH' 
                        ? 'bg-red-100 text-red-800' 
                        : 'bg-amber-100 text-amber-800'
                    }`}>
                      {recommendation.priority}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    {recommendation.description}
                  </p>
                  <p className="text-xs text-gray-500">
                    <strong>Expected Impact:</strong> {recommendation.expectedImpact}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-8 border-t border-gray-200">
        <Button variant="ghost" onClick={onPrevSection} className="flex items-center space-x-2">
          <ChevronLeft className="w-4 h-4" />
          <span>Previous Section</span>
        </Button>
        <Button onClick={onNextSection} className="flex items-center space-x-2">
          <span>Next Section</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
