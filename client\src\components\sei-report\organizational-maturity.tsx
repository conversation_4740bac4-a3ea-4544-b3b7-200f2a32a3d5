import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/ui/card";
import { ChevronRight, ChevronLeft } from "lucide-react";
import { But<PERSON> } from "@/ui/button";

interface OrganizationalMaturityProps {
  onNextSection: () => void;
  onPrevSection: () => void;
}

export function OrganizationalMaturity({ onNextSection, onPrevSection }: OrganizationalMaturityProps) {
  return (
    <div className="p-6 space-y-8">
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-4">Organizational Maturity Assessment</h2>
        <p className="text-gray-700 mb-6">
          Assessment of Verizon's organizational capabilities, culture, and readiness for strategic transformation initiatives.
        </p>

        <Card>
          <CardHeader>
            <CardTitle>Maturity Framework</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                <div>
                  <h4 className="font-semibold text-gray-900">Digital Transformation</h4>
                  <p className="text-sm text-gray-600">Advanced digital capabilities and automation</p>
                </div>
                <div className="text-2xl font-bold text-green-600">4.2/5</div>
              </div>
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div>
                  <h4 className="font-semibold text-gray-900">Innovation Culture</h4>
                  <p className="text-sm text-gray-600">Organizational openness to innovation and change</p>
                </div>
                <div className="text-2xl font-bold text-blue-600">3.8/5</div>
              </div>
              <div className="flex items-center justify-between p-4 bg-amber-50 rounded-lg">
                <div>
                  <h4 className="font-semibold text-gray-900">Agility & Responsiveness</h4>
                  <p className="text-sm text-gray-600">Speed of decision-making and implementation</p>
                </div>
                <div className="text-2xl font-bold text-amber-600">3.5/5</div>
              </div>
              <div className="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                <div>
                  <h4 className="font-semibold text-gray-900">Data-Driven Decision Making</h4>
                  <p className="text-sm text-gray-600">Use of analytics and data in strategic decisions</p>
                </div>
                <div className="text-2xl font-bold text-red-600">4.0/5</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-8 border-t border-gray-200">
        <Button variant="ghost" onClick={onPrevSection} className="flex items-center space-x-2">
          <ChevronLeft className="w-4 h-4" />
          <span>Previous Section</span>
        </Button>
        <Button onClick={onNextSection} className="flex items-center space-x-2">
          <span>Next Section</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
