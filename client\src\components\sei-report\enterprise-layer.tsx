import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/ui/card";
import { ChevronR<PERSON>, ChevronLeft } from "lucide-react";
import { But<PERSON> } from "@/ui/button";

interface EnterpriseLayerProps {
  onNextSection: () => void;
  onPrevSection: () => void;
}

export function EnterpriseLayer({ onNextSection, onPrevSection }: EnterpriseLayerProps) {
  return (
    <div className="p-6 space-y-8">
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-4">Enterprise Layer Analysis</h2>
        <p className="text-gray-700 mb-6">
          The enterprise layer provides strategic oversight and governance across Verizon's business units, 
          ensuring alignment with corporate objectives and optimal resource allocation.
        </p>

        <Card>
          <CardHeader>
            <CardTitle>Enterprise Architecture Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold text-gray-900">Corporate Strategy</h4>
                <p className="text-sm text-gray-600">Unified strategic vision across all business units</p>
              </div>
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-semibold text-gray-900">Resource Optimization</h4>
                <p className="text-sm text-gray-600">Efficient allocation of capital and operational resources</p>
              </div>
              <div className="border-l-4 border-amber-500 pl-4">
                <h4 className="font-semibold text-gray-900">Risk Management</h4>
                <p className="text-sm text-gray-600">Enterprise-wide risk assessment and mitigation</p>
              </div>
              <div className="border-l-4 border-red-500 pl-4">
                <h4 className="font-semibold text-gray-900">Performance Monitoring</h4>
                <p className="text-sm text-gray-600">Comprehensive KPI tracking and reporting</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-8 border-t border-gray-200">
        <Button variant="ghost" onClick={onPrevSection} className="flex items-center space-x-2">
          <ChevronLeft className="w-4 h-4" />
          <span>Previous Section</span>
        </Button>
        <Button onClick={onNextSection} className="flex items-center space-x-2">
          <span>Next Section</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
