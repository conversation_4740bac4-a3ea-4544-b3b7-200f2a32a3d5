import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/ui/toaster";
import { TooltipProvider } from "@/ui/tooltip";
import { AuthProvider, useAuth } from "./hooks/use-auth";
import { CompanyProvider } from "@/contexts/company-context";
import Login from "@/pages/login";
import Dashboard from "@/pages/dashboard";
import SEIReport from "@/pages/sei-report";
import JoyceAgent from "@/pages/joyce-agent";
import Intelligence from "@/pages/intelligence";
import ExecutionReadiness from "@/pages/execution-readiness";
import DigitalMirror from "@/pages/digital-mirror";
import Execution from "@/pages/execution";
import Mine from "@/pages/mine";
import Forge from "@/pages/forge";
import Refinery from "@/pages/refinery";
import NotFound from "@/pages/not-found";

function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, isLoading } = useAuth();
  
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }
  
  if (!user) {
    return <Login />;
  }
  
  return <>{children}</>;
}

function Router() {
  return (
    <Switch>
      <Route path="/login" component={Login} />
      <Route path="/">
        <ProtectedRoute>
          <Dashboard />
        </ProtectedRoute>
      </Route>
      <Route path="/intelligence">
        <ProtectedRoute>
          <Intelligence />
        </ProtectedRoute>
      </Route>
      <Route path="/execution-readiness">
        <ProtectedRoute>
          <ExecutionReadiness />
        </ProtectedRoute>
      </Route>
      <Route path="/digital-mirror">
        <ProtectedRoute>
          <DigitalMirror />
        </ProtectedRoute>
      </Route>
      <Route path="/sei-report/:section?">
        <ProtectedRoute>
          <SEIReport />
        </ProtectedRoute>
      </Route>
      <Route path="/execution">
        <ProtectedRoute>
          <Execution />
        </ProtectedRoute>
      </Route>
      <Route path="/mine">
        <ProtectedRoute>
          <Mine />
        </ProtectedRoute>
      </Route>
      <Route path="/forge">
        <ProtectedRoute>
          <Forge />
        </ProtectedRoute>
      </Route>
      <Route path="/refinery">
        <ProtectedRoute>
          <Refinery />
        </ProtectedRoute>
      </Route>
      <Route path="/joyce-agent">
        <ProtectedRoute>
          <JoyceAgent />
        </ProtectedRoute>
      </Route>
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <CompanyProvider>
          <TooltipProvider>
            <Toaster />
            <Router />
          </TooltipProvider>
        </CompanyProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
