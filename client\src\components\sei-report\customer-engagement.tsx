import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/ui/card";
import { Bad<PERSON> } from "@/ui/badge";
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, BarChart, Bar } from "recharts";
import { ChevronRight, ChevronLeft, Lightbulb, TrendingUp } from "lucide-react";
import { <PERSON><PERSON> } from "@/ui/button";

interface CustomerEngagementProps {
  onNextSection: () => void;
  onPrevSection: () => void;
}

export function CustomerEngagement({ onNextSection, onPrevSection }: CustomerEngagementProps) {
  const segmentationData = [
    { name: "Premium", value: 32, color: "#3B82F6" },
    { name: "Standard", value: 45, color: "#93C5FD" },
    { name: "Basic", value: 23, color: "#D1D5DB" },
  ];

  const churnTrendData = [
    { month: "Jan", premium: 0.6, standard: 1.1, basic: 1.8 },
    { month: "Feb", premium: 0.58, standard: 1.08, basic: 1.75 },
    { month: "Mar", premium: 0.55, standard: 1.05, basic: 1.7 },
    { month: "Apr", premium: 0.52, standard: 1.02, basic: 1.65 },
    { month: "May", premium: 0.49, standard: 0.98, basic: 1.6 },
    { month: "Jun", premium: 0.47, standard: 0.95, basic: 1.55 },
    { month: "Jul", premium: 0.45, standard: 0.93, basic: 1.52 },
    { month: "Aug", premium: 0.44, standard: 0.91, basic: 1.49 },
  ];

  const cltvData = [
    { segment: "Premium", verizon: 2100, benchmark: 1950 },
    { segment: "Standard", verizon: 1400, benchmark: 1300 },
    { segment: "Basic", verizon: 850, benchmark: 780 },
  ];

  const satisfactionData = [
    { stage: "Consideration", current: 85, target: 90 },
    { stage: "Onboarding", current: 92, target: 95 },
    { stage: "Usage", current: 88, target: 93 },
    { stage: "Renewal", current: 84, target: 90 },
  ];

  return (
    <div className="p-6 space-y-8">
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-4">Customer Engagement & Lifetime Value</h2>
        <p className="text-gray-700 mb-6">
          Verizon (VZ) serves a diverse customer base across consumer, business, and premium wireless segments. Understanding and enhancing customer engagement 
          and lifetime value is central to Verizon's strategy, as the company seeks to deepen relationships, reduce churn, and drive long-term growth through tailored 
          experiences and innovative offerings.
        </p>

        {/* Charts Section */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          {/* Customer Segmentation Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Segmentation</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={segmentationData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={120}
                    paddingAngle={0}
                    dataKey="value"
                  >
                    {segmentationData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>
              <div className="flex justify-center space-x-6 mt-4">
                {segmentationData.map((item, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }}></div>
                    <span className="text-sm text-gray-700">{item.name}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Churn Rate Trends */}
          <Card>
            <CardHeader>
              <CardTitle>Churn Rate Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={churnTrendData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                  <XAxis dataKey="month" axisLine={false} tickLine={false} />
                  <YAxis axisLine={false} tickLine={false} />
                  <Line 
                    type="monotone" 
                    dataKey="premium" 
                    stroke="#3B82F6" 
                    strokeWidth={2}
                    dot={{ fill: "#3B82F6", strokeWidth: 2, r: 4 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="standard" 
                    stroke="#EF4444" 
                    strokeWidth={2}
                    dot={{ fill: "#EF4444", strokeWidth: 2, r: 4 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="basic" 
                    stroke="#10B981" 
                    strokeWidth={2}
                    dot={{ fill: "#10B981", strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Customer Analytics */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          {/* Customer Lifetime Value */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Lifetime Value by Segment</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={cltvData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                  <XAxis dataKey="segment" axisLine={false} tickLine={false} />
                  <YAxis axisLine={false} tickLine={false} />
                  <Bar dataKey="verizon" fill="#EF4444" />
                  <Bar dataKey="benchmark" fill="#10B981" />
                </BarChart>
              </ResponsiveContainer>
              <div className="flex justify-center space-x-6 mt-4">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded"></div>
                  <span className="text-sm text-gray-700">Verizon</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded"></div>
                  <span className="text-sm text-gray-700">Industry Benchmark</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Customer Journey Satisfaction */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Journey Satisfaction</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={satisfactionData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                  <XAxis dataKey="stage" axisLine={false} tickLine={false} />
                  <YAxis domain={[70, 100]} axisLine={false} tickLine={false} />
                  <Line 
                    type="monotone" 
                    dataKey="current" 
                    stroke="#EF4444" 
                    strokeWidth={3}
                    dot={{ fill: "#EF4444", strokeWidth: 2, r: 5 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="target" 
                    stroke="#9CA3AF" 
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    dot={{ fill: "#9CA3AF", strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Key Insights */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 mb-4">
                <Lightbulb className="w-5 h-5 text-blue-600" />
                <h3 className="font-semibold text-gray-900">Key Engagement Insights</h3>
              </div>
              <ul className="space-y-3">
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-700">Consumer customers show high engagement with bundled wireless and broadband offerings, driving increased stickiness and lower churn rates.</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-700">Digital self-service adoption is rising, with more consumers managing accounts and services through Verizon's mobile app and online platforms.</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-700">Customer satisfaction is closely linked to network reliability and speed, with positive feedback on 5G rollout in key markets.</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 mb-4">
                <TrendingUp className="w-5 h-5 text-blue-600" />
                <h3 className="font-semibold text-gray-900">Enhancement Opportunities</h3>
              </div>
              <ul className="space-y-3">
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-700">Expand personalized offers and loyalty rewards to increase retention and upsell opportunities among existing consumer customers.</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-700">Enhance omnichannel support by integrating digital and in-store experiences for seamless customer journeys.</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-700">Leverage data analytics to proactively address service issues and improve first-contact resolution rates.</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Action Items */}
        <Card>
          <CardHeader>
            <CardTitle>Strategic Action Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">Enhance Digital Self-Service Platforms</h4>
                  <p className="text-sm text-gray-600">Invest in intuitive digital tools to empower customers and reduce support friction.</p>
                </div>
                <Badge variant="destructive">HIGH</Badge>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">Expand Loyalty and Rewards Programs</h4>
                  <p className="text-sm text-gray-600">Drive retention and upsell through targeted incentives and exclusive benefits.</p>
                </div>
                <Badge variant="destructive">HIGH</Badge>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">Personalize Customer Journeys</h4>
                  <p className="text-sm text-gray-600">Utilize data-driven insights to tailor experiences and communications for each segment.</p>
                </div>
                <Badge variant="secondary">MEDIUM</Badge>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">Strengthen Omnichannel Support</h4>
                  <p className="text-sm text-gray-600">Integrate digital, phone, and in-store support for seamless customer experiences.</p>
                </div>
                <Badge variant="secondary">MEDIUM</Badge>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">Accelerate 5G and Advanced Service Adoption</h4>
                  <p className="text-sm text-gray-600">Promote new network capabilities and value-added services to increase engagement.</p>
                </div>
                <Badge variant="secondary">MEDIUM</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-8 border-t border-gray-200">
        <Button variant="ghost" onClick={onPrevSection} className="flex items-center space-x-2">
          <ChevronLeft className="w-4 h-4" />
          <span>Previous Section</span>
        </Button>
        <Button onClick={onNextSection} className="flex items-center space-x-2">
          <span>Next Section</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
