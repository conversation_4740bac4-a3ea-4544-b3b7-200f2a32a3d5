import { Home, Lightbulb, Target, BarChart3, User, X } from "lucide-react";
import { Link, useLocation } from "wouter";
import { But<PERSON> } from "@/ui/button";
import { useAuth } from "@/hooks/use-auth";

export function Sidebar() {
  const [location] = useLocation();
  const { user, logout } = useAuth();

  const navigation = [
    { name: "Home", href: "/", icon: Home },
    { name: "SEI Report", href: "/sei-report", icon: BarChart3 },
    { name: "Joyce Agent", href: "/joyce-agent", icon: Target },
  ];

  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col h-screen">
      {/* Logo and Company Selector */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2 mb-4">
          <div className="w-8 h-8 bg-rejoyce-blue rounded flex items-center justify-center">
            <span className="text-white font-bold text-sm">R</span>
          </div>
          <span className="font-bold text-xl text-gray-900">rejoyce</span>
        </div>
        
        <div className="space-y-2">
          <div className="text-sm font-medium text-gray-700">Focus Company</div>
          <div className="flex items-center space-x-2 bg-gray-100 rounded px-3 py-2">
            <span className="font-medium">VZ</span>
            <X className="w-3 h-3 text-gray-400 cursor-pointer" />
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          <li>
            <Link href="/">
              <a className={`flex items-center space-x-3 rounded-lg px-3 py-2 ${
                location === "/" 
                  ? "bg-rejoyce-green text-white" 
                  : "text-gray-700 hover:bg-gray-100"
              }`}>
                <Home className="w-5 h-5" />
                <span>Digital Mirror</span>
              </a>
            </Link>
          </li>
          <li>
            <div className="flex items-center space-x-3 text-gray-700 px-3 py-2">
              <Lightbulb className="w-5 h-5" />
              <span>Insights</span>
            </div>
            <ul className="ml-8 mt-2 space-y-1">
              <li>
                <span className="block text-gray-600 px-3 py-1 text-sm bg-gray-100 rounded">
                  Explore
                </span>
              </li>
            </ul>
          </li>
          <li>
            <div className="flex items-center space-x-3 text-gray-700 px-3 py-2">
              <Target className="w-5 h-5" />
              <span>Focus</span>
            </div>
          </li>
          <li>
            <Link href="/sei-report/executive-summary">
              <a className={`flex items-center space-x-3 rounded-lg px-3 py-2 ${
                location.startsWith("/sei-report") 
                  ? "bg-rejoyce-green text-white" 
                  : "text-gray-700 hover:bg-gray-100"
              }`}>
                <BarChart3 className="w-5 h-5" />
                <span>SEI Report</span>
              </a>
            </Link>
          </li>
          <li>
            <Link href="/joyce-agent">
              <a className={`flex items-center space-x-3 rounded-lg px-3 py-2 ${
                location === "/joyce-agent" 
                  ? "bg-rejoyce-green text-white" 
                  : "text-gray-700 hover:bg-gray-100"
              }`}>
                <Target className="w-5 h-5" />
                <span>Joyce Agent</span>
              </a>
            </Link>
          </li>
        </ul>
      </nav>

      {/* User Section */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 text-gray-700">
            <User className="w-4 h-4" />
            <span className="text-sm">{user?.username || "Analyst"}</span>
          </div>
          <Button variant="ghost" size="sm" onClick={logout}>
            Logout
          </Button>
        </div>
      </div>
    </div>
  );
}
