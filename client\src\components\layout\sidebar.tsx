import { Home, Lightbulb, Target, BarChart3, User, X, Search, Check, Brain, Zap, ChevronDown, ChevronRight } from "lucide-react";
import { Link, useLocation } from "wouter";
import { But<PERSON> } from "@/ui/button";
import { Input } from "@/ui/input";
import { useAuth } from "@/hooks/use-auth";
import { useCompany } from "@/contexts/company-context";
import { CompanyService, CompanyInfo } from "@/services/company-service";
import { useState, useEffect } from "react";

export function Sidebar() {
  const [location] = useLocation();
  const { user, logout } = useAuth();
  const { selectedCompany, setSelectedCompany, companyDisplayName } = useCompany();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<CompanyInfo[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [intelligenceExpanded, setIntelligenceExpanded] = useState(
    location.startsWith("/sei-report") || location === "/execution-readiness" || location === "/digital-mirror"
  );
  const [executionExpanded, setExecutionExpanded] = useState(
    location === "/mine" || location === "/forge" || location === "/refinery"
  );
  const [selectedCompanyFullName, setSelectedCompanyFullName] = useState<string>('');
  const companyService = CompanyService.getInstance();

  useEffect(() => {
    const searchCompanies = async () => {
      if (showSearch) {
        const results = await companyService.searchCompanies(searchQuery);
        setSearchResults(results);
      }
    };
    searchCompanies();
  }, [searchQuery, showSearch]);

  useEffect(() => {
    // Auto-expand sections based on current route
    if (location.startsWith("/sei-report") || location === "/execution-readiness" || location === "/digital-mirror") {
      setIntelligenceExpanded(true);
    }
    if (location === "/mine" || location === "/forge" || location === "/refinery") {
      setExecutionExpanded(true);
    }
  }, [location]);

  useEffect(() => {
    // Fetch full company name when selected company changes
    const fetchCompanyFullName = async () => {
      if (selectedCompany) {
        try {
          const fullName = await companyService.getCompanyFullName(selectedCompany);
          setSelectedCompanyFullName(fullName);
        } catch (error) {
          console.error('Error fetching company full name:', error);
          setSelectedCompanyFullName(selectedCompany.toUpperCase());
        }
      }
    };

    fetchCompanyFullName();
  }, [selectedCompany, companyService]);

  const handleCompanySelect = (companyCode: string) => {
    setSelectedCompany(companyCode);
    setShowSearch(false);
    setSearchQuery('');
  };

  const handleClearCompany = () => {
    setShowSearch(true);
    setSearchQuery('');
  };

  const navigation = [
    { name: "Home", href: "/", icon: Home },
    { name: "SEI Report", href: "/sei-report", icon: BarChart3 },
    { name: "Joyce Agent", href: "/joyce-agent", icon: Target },
  ];

  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col h-screen">
      {/* Logo and Company Selector */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center mb-4">
          <img 
            src="/images/rejoyce-logo--black.png" 
            alt="Rejoyce" 
            className="h-8 w-auto"
          />
        </div>
        
        <div className="space-y-2">
          <div className="text-sm font-medium text-gray-700">Focus Company</div>
          
          {!showSearch ? (
            <div className="flex items-center justify-between bg-gray-100 rounded px-3 py-2">
              <div className="flex-1">
                <div className="font-medium">{companyDisplayName}</div>
                <div className="text-xs text-gray-500">{selectedCompanyFullName}</div>
              </div>
              <X className="w-3 h-3 text-gray-400 cursor-pointer" onClick={handleClearCompany} />
            </div>
          ) : (
            <div className="space-y-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search companies..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 text-sm"
                  autoFocus
                />
              </div>
              
              {searchResults.length > 0 && (
                <div className="max-h-40 overflow-y-auto bg-white border border-gray-200 rounded-md shadow-sm">
                  {searchResults.map((company) => (
                    <div
                      key={company.code}
                      className="px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                      onClick={() => handleCompanySelect(company.code)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-sm">{company.displayName}</div>
                          <div className="text-xs text-gray-500">{company.fullName}</div>
                        </div>
                        {selectedCompany === company.code && (
                          <Check className="w-4 h-4 text-green-600" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {searchResults.length === 0 && searchQuery && (
                <div className="text-sm text-gray-500 px-3 py-2 bg-gray-50 rounded">
                  No companies found matching "{searchQuery}"
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {/* Home */}
          <li>
            <Link href="/">
              <a className={`flex items-center space-x-3 rounded-lg px-3 py-2 ${
                location === "/" 
                  ? "bg-rejoyce-green text-white" 
                  : "text-gray-700 hover:bg-gray-100"
              }`}>
                <Home className="w-5 h-5" />
                <span>Home</span>
              </a>
            </Link>
          </li>

          {/* Intelligence */}
          <li>
            <div 
              className="flex items-center justify-between cursor-pointer"
              onClick={() => setIntelligenceExpanded(!intelligenceExpanded)}
            >
              <div className="flex items-center space-x-3 text-gray-700 px-3 py-2">
                <Brain className="w-5 h-5" />
                <span>Intelligence</span>
              </div>
              {intelligenceExpanded ? 
                <ChevronDown className="w-4 h-4 text-gray-400" /> : 
                <ChevronRight className="w-4 h-4 text-gray-400" />
              }
            </div>
            {intelligenceExpanded && (
              <ul className="ml-8 mt-1 space-y-1">
                <li>
                  <Link href="/execution-readiness">
                    <a className={`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm ${
                      location === "/execution-readiness" 
                        ? "bg-rejoyce-green text-white" 
                        : "text-gray-600 hover:bg-gray-100"
                    }`}>
                      <span>Execution Readiness Analysis</span>
                    </a>
                  </Link>
                </li>
                <li>
                  <Link href="/digital-mirror">
                    <a className={`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm ${
                      location === "/digital-mirror" 
                        ? "bg-rejoyce-green text-white" 
                        : "text-gray-600 hover:bg-gray-100"
                    }`}>
                      <span>Digital Mirror</span>
                    </a>
                  </Link>
                </li>
                <li>
                  <Link href="/sei-report/executive-summary">
                    <a className={`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm ${
                      location.startsWith("/sei-report") 
                        ? "bg-rejoyce-green text-white" 
                        : "text-gray-600 hover:bg-gray-100"
                    }`}>
                      <span>SEI Report</span>
                    </a>
                  </Link>
                </li>
              </ul>
            )}
          </li>

          {/* Execution */}
          <li>
            <div 
              className="flex items-center justify-between cursor-pointer"
              onClick={() => setExecutionExpanded(!executionExpanded)}
            >
              <div className="flex items-center space-x-3 text-gray-700 px-3 py-2">
                <Zap className="w-5 h-5" />
                <span>Execution</span>
              </div>
              {executionExpanded ? 
                <ChevronDown className="w-4 h-4 text-gray-400" /> : 
                <ChevronRight className="w-4 h-4 text-gray-400" />
              }
            </div>
            {executionExpanded && (
              <ul className="ml-8 mt-1 space-y-1">
                <li>
                  <Link href="/mine">
                    <a className={`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm ${
                      location === "/mine" 
                        ? "bg-rejoyce-green text-white" 
                        : "text-gray-600 hover:bg-gray-100"
                    }`}>
                      <span>Mine</span>
                    </a>
                  </Link>
                </li>
                <li>
                  <Link href="/forge">
                    <a className={`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm ${
                      location === "/forge" 
                        ? "bg-rejoyce-green text-white" 
                        : "text-gray-600 hover:bg-gray-100"
                    }`}>
                      <span>Forge</span>
                    </a>
                  </Link>
                </li>
                <li>
                  <Link href="/refinery">
                    <a className={`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm ${
                      location === "/refinery" 
                        ? "bg-rejoyce-green text-white" 
                        : "text-gray-600 hover:bg-gray-100"
                    }`}>
                      <span>Refinery</span>
                    </a>
                  </Link>
                </li>
              </ul>
            )}
          </li>
        </ul>
      </nav>

      {/* User Section */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 text-gray-700">
            <User className="w-4 h-4" />
            <span className="text-sm">{user?.username || "Analyst"}</span>
          </div>
          <Button variant="ghost" size="sm" onClick={logout}>
            Logout
          </Button>
        </div>
      </div>
    </div>
  );
}
