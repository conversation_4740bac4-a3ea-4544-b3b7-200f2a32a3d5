import { useState } from "react";
import { Sidebar } from "@/components/layout/sidebar";
import { Header } from "@/components/layout/header";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/ui/card";
import { Input } from "@/ui/input";
import { <PERSON><PERSON> } from "@/ui/button";
import { Send, Bot, User } from "lucide-react";

interface Message {
  id: string;
  text: string;
  sender: "user" | "agent";
  timestamp: Date;
}

export default function JoyceAgent() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      text: "Hello! I'm <PERSON>, your Strategic Execution Intelligence agent. I can help you analyze Verizon's performance, explore strategic insights, and answer questions about the SEI report. How can I assist you today?",
      sender: "agent",
      timestamp: new Date(),
    },
  ]);
  const [inputText, setInputText] = useState("");

  const handleSendMessage = () => {
    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText,
      sender: "user",
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);

    // Simulate AI response
    setTimeout(() => {
      const agentResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: "Thank you for your question about Verizon's strategic execution. Based on the current data, I can provide insights on key performance indicators, competitive positioning, and strategic recommendations. What specific aspect would you like me to analyze?",
        sender: "agent",
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, agentResponse]);
    }, 1000);

    setInputText("");
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="min-h-screen flex bg-gray-50">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Header 
          title="Joyce Agent - Strategic Intelligence Assistant"
          subtitle="AI-powered analysis and insights for strategic decision making"
          showRefresh={false}
          showPeriod={false}
        />
        
        <div className="flex-1 p-6">
          <div className="max-w-4xl mx-auto h-full flex flex-col">
            {/* Chat Interface */}
            <Card className="flex-1 flex flex-col">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Bot className="w-5 h-5 text-blue-600" />
                  <span>Joyce Strategic Intelligence Agent</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col">
                {/* Messages */}
                <div className="flex-1 overflow-y-auto space-y-4 mb-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}
                    >
                      <div
                        className={`max-w-3xl rounded-lg px-4 py-3 ${
                          message.sender === "user"
                            ? "bg-blue-600 text-white"
                            : "bg-gray-100 text-gray-900"
                        }`}
                      >
                        <div className="flex items-start space-x-2">
                          {message.sender === "agent" && (
                            <Bot className="w-4 h-4 mt-1 text-blue-600" />
                          )}
                          {message.sender === "user" && (
                            <User className="w-4 h-4 mt-1 text-white" />
                          )}
                          <div>
                            <p className="text-sm">{message.text}</p>
                            <p className={`text-xs mt-1 ${
                              message.sender === "user" ? "text-blue-100" : "text-gray-500"
                            }`}>
                              {message.timestamp.toLocaleTimeString()}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Input */}
                <div className="flex space-x-2">
                  <Input
                    placeholder="Ask Joyce about Verizon's strategic performance..."
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="flex-1"
                  />
                  <Button onClick={handleSendMessage} disabled={!inputText.trim()}>
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="cursor-pointer hover:bg-gray-50 transition-colors">
                <CardContent className="pt-6">
                  <h3 className="font-semibold text-gray-900 mb-2">Performance Analysis</h3>
                  <p className="text-sm text-gray-600">
                    Analyze Verizon's key performance indicators and trends
                  </p>
                </CardContent>
              </Card>
              <Card className="cursor-pointer hover:bg-gray-50 transition-colors">
                <CardContent className="pt-6">
                  <h3 className="font-semibold text-gray-900 mb-2">Strategic Insights</h3>
                  <p className="text-sm text-gray-600">
                    Explore strategic recommendations and market positioning
                  </p>
                </CardContent>
              </Card>
              <Card className="cursor-pointer hover:bg-gray-50 transition-colors">
                <CardContent className="pt-6">
                  <h3 className="font-semibold text-gray-900 mb-2">Competitive Analysis</h3>
                  <p className="text-sm text-gray-600">
                    Compare Verizon's performance against industry benchmarks
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
