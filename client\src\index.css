@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(60, 4.8%, 95.9%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
  
  /* Rejoyce specific colors */
  --rejoyce-blue: hsl(207, 90%, 54%);
  --rejoyce-dark-blue: hsl(217, 91%, 35%);
  --rejoyce-coral: hsl(0, 84.2%, 60.2%);
  --rejoyce-green: hsl(153, 73%, 51%);
  --rejoyce-amber: hsl(37, 92%, 50%);
  --rejoyce-gray: hsl(215, 16%, 47%);
  
  /* Chart colors */
  --chart-1: hsl(207, 90%, 54%);
  --chart-2: hsl(0, 84.2%, 60.2%);
  --chart-3: hsl(153, 73%, 51%);
  --chart-4: hsl(37, 92%, 50%);
  --chart-5: hsl(271, 81%, 56%);

  /* Sidebar colors */
  --sidebar-background: hsl(0, 0%, 100%);
  --sidebar-foreground: hsl(20, 14.3%, 4.1%);
  --sidebar-primary: hsl(153, 73%, 51%);
  --sidebar-primary-foreground: hsl(0, 0%, 100%);
  --sidebar-accent: hsl(60, 4.8%, 95.9%);
  --sidebar-accent-foreground: hsl(24, 9.8%, 10%);
  --sidebar-border: hsl(20, 5.9%, 90%);
  --sidebar-ring: hsl(20, 14.3%, 4.1%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
  
  /* Sidebar colors for dark mode */
  --sidebar-background: hsl(240, 5.9%, 10%);
  --sidebar-foreground: hsl(240, 4.8%, 95.9%);
  --sidebar-primary: hsl(224, 71.4%, 4.1%);
  --sidebar-primary-foreground: hsl(210, 20%, 98%);
  --sidebar-accent: hsl(240, 3.7%, 15.9%);
  --sidebar-accent-foreground: hsl(240, 4.8%, 95.9%);
  --sidebar-border: hsl(240, 3.7%, 15.9%);
  --sidebar-ring: hsl(217.2, 91.2%, 59.8%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

@layer utilities {
  .rejoyce-blue {
    color: var(--rejoyce-blue);
  }
  
  .bg-rejoyce-blue {
    background-color: var(--rejoyce-blue);
  }
  
  .rejoyce-coral {
    color: var(--rejoyce-coral);
  }
  
  .bg-rejoyce-coral {
    background-color: var(--rejoyce-coral);
  }
  
  .rejoyce-green {
    color: var(--rejoyce-green);
  }
  
  .bg-rejoyce-green {
    background-color: var(--rejoyce-green);
  }
  
  .rejoyce-amber {
    color: var(--rejoyce-amber);
  }
  
  .bg-rejoyce-amber {
    background-color: var(--rejoyce-amber);
  }
}
