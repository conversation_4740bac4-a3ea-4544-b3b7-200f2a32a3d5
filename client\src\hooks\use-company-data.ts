import { useState, useEffect } from 'react';

interface ExecutiveSnapshot {
  investabilityGrade: {
    value: string;
    description: string;
    status: string;
  };
  joyScore: {
    value: number;
    maxValue: number;
    description: string;
    status: string;
  };
  fogScore: {
    value: number;
    maxValue: number;
    description: string;
    status: string;
  };
  roiPotential: {
    value: string;
    description: string;
    status: string;
  };
}

interface ListItem {
  text: string;
  status: string;
}

interface StakeholderTakeaway {
  priority: string;
  text: string;
  status: string;
}

interface ExecutiveSummaryData {
  companySymbol: string;
  companyName: string;
  reportSection: string;
  lastUpdated: string;
  executiveSnapshot: ExecutiveSnapshot;
  summaryAnalysis: {
    text: string;
  };
  keyStrengths: ListItem[];
  riskFlags: ListItem[];
  strategicObjectives: ListItem[];
  strategicInsights: ListItem[];
  stakeholderTakeaways: {
    investor: StakeholderTakeaway[];
    executive: StakeholderTakeaway[];
  };
}

export function useCompanyData(companySymbol: string, section: string) {
  const [data, setData] = useState<ExecutiveSummaryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/data/companies/${companySymbol.toLowerCase()}/${section}.json`);
        
        if (!response.ok) {
          throw new Error(`Failed to load ${section} data for ${companySymbol}`);
        }
        
        const jsonData = await response.json();
        setData(jsonData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data');
        console.error('Error loading company data:', err);
      } finally {
        setLoading(false);
      }
    };

    if (companySymbol && section) {
      loadData();
    }
  }, [companySymbol, section]);

  return { data, loading, error };
}