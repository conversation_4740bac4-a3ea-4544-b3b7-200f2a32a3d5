import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { Progress } from "@/ui/progress";
import { ChevronLeft } from "lucide-react";
import { Button } from "@/ui/button";
import { ExecutionPriority } from "@shared/schema";

interface ExecutionRoadmapProps {
  onPrevSection: () => void;
}

export function ExecutionRoadmap({ onPrevSection }: ExecutionRoadmapProps) {
  const { data: priorities } = useQuery<ExecutionPriority[]>({
    queryKey: ["/api/companies/VZ/priorities"],
  });

  return (
    <div className="p-6 space-y-8">
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-4">Execution Roadmap</h2>
        <p className="text-gray-700 mb-6">
          Strategic implementation plan with clear timelines, ownership, and progress tracking for key initiatives.
        </p>

        <div className="space-y-6">
          {priorities?.map((priority) => (
            <Card key={priority.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{priority.title}</CardTitle>
                  <Badge variant={priority.priority === "HIGH" ? "destructive" : "secondary"}>
                    {priority.priority}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">{priority.description}</p>
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div>
                    <div className="text-sm font-medium text-gray-700">Category</div>
                    <div className="text-sm text-gray-600">{priority.category}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-700">Owner</div>
                    <div className="text-sm text-gray-600">{priority.owner}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-700">Timeline</div>
                    <div className="text-sm text-gray-600">{priority.timeline}</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Progress</span>
                    <span className="text-sm text-gray-600">{priority.progress}%</span>
                  </div>
                  <Progress value={priority.progress} className="h-2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Implementation Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                <div className="flex-1">
                  <div className="font-medium">Q3 2023 - Q4 2023</div>
                  <div className="text-sm text-gray-600">5G Network Expansion, Customer Retention Programs</div>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-4 h-4 bg-amber-500 rounded-full"></div>
                <div className="flex-1">
                  <div className="font-medium">Q4 2023 - Q1 2024</div>
                  <div className="text-sm text-gray-600">Capital Allocation Optimization, Digital Transformation Phase 1</div>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <div className="font-medium">Q1 2024 - Q2 2024</div>
                  <div className="text-sm text-gray-600">Advanced Analytics Implementation, Process Automation</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-8 border-t border-gray-200">
        <Button variant="ghost" onClick={onPrevSection} className="flex items-center space-x-2">
          <ChevronLeft className="w-4 h-4" />
          <span>Previous Section</span>
        </Button>
        <div></div>
      </div>
    </div>
  );
}
