import { useParams, useLocation } from "wouter";
import { Sidebar } from "@/components/layout/sidebar";
import { Header } from "@/components/layout/header";
import { <PERSON><PERSON> } from "@/ui/button";
import { List, Filter } from "lucide-react";
import { ExecutiveSummary } from "@/components/sei-report/executive-summary";
import { EnterpriseLayer } from "@/components/sei-report/enterprise-layer";
import { ProductsServices } from "@/components/sei-report/products-services";
import { CustomerEngagement } from "@/components/sei-report/customer-engagement";
import { OrganizationalMaturity } from "@/components/sei-report/organizational-maturity";
import { ExecutionRoadmap } from "@/components/sei-report/execution-roadmap";

const tabs = [
  { id: "executive-summary", label: "Executive Summary", component: ExecutiveSummary },
  { id: "enterprise-layer", label: "Enterprise Layer", component: EnterpriseLayer },
  { id: "products-services", label: "Products & Services", component: ProductsServices },
  { id: "customer-engagement", label: "Customer Engagement", component: CustomerEngagement },
  { id: "organizational-maturity", label: "Organizational Maturity", component: OrganizationalMaturity },
  { id: "execution-roadmap", label: "Execution Roadmap", component: ExecutionRoadmap },
];

export default function SEIReport() {
  const { section } = useParams<{ section?: string }>();
  const [, setLocation] = useLocation();
  
  // Default to executive-summary if no section is provided or invalid section
  const activeTab = section && tabs.find(tab => tab.id === section) ? section : "executive-summary";
  
  const currentTabIndex = tabs.findIndex(tab => tab.id === activeTab);
  const ActiveComponent = tabs[currentTabIndex]?.component;

  const handleNextSection = () => {
    const nextIndex = Math.min(currentTabIndex + 1, tabs.length - 1);
    const nextSection = tabs[nextIndex].id;
    setLocation(`/sei-report/${nextSection}`);
  };

  const handlePrevSection = () => {
    const prevIndex = Math.max(currentTabIndex - 1, 0);
    const prevSection = tabs[prevIndex].id;
    setLocation(`/sei-report/${prevSection}`);
  };

  return (
    <div className="min-h-screen flex bg-gray-50">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Header 
          title="Strategic Execution Intelligence Report"
          subtitle="Comprehensive performance analysis and strategic execution intelligence"
        />
        
        {/* Report Controls */}
        <div className="bg-white px-6 py-4 border-b border-gray-200">
          <div className="flex space-x-4">
            <Button variant="outline" className="flex items-center space-x-2">
              <List className="w-4 h-4" />
              <span>Table of Contents</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <span>Strategic Focus Customization</span>
            </Button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white border-b border-gray-200 px-6">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setLocation(`/sei-report/${tab.id}`)}
                className={`py-4 border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? "border-blue-600 text-blue-600 font-medium"
                    : "border-transparent text-gray-600 hover:text-gray-900"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="flex-1 overflow-auto bg-gray-50">
          {ActiveComponent && (
            <ActiveComponent 
              onNextSection={handleNextSection}
              onPrevSection={handlePrevSection}
            />
          )}
        </div>
      </div>
    </div>
  );
}
