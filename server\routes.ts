import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { loginSchema } from "@shared/schema";
import bcrypt from "bcrypt";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { anthropic } from "@ai-sdk/anthropic";
import { streamText } from "ai";
import { JoyceContextService } from "./joyce-context";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export async function registerRoutes(app: Express): Promise<Server> {
  // Joyce Chat API route
  app.post("/api/chat", async (req, res) => {
    try {
      const { messages, companySymbol } = req.body;
      
      // Build context based on company selection and prompts
      const context = await JoyceContextService.buildContext(companySymbol);
      
      const result = await streamText({
        model: anthropic("claude-3-5-sonnet-20241022", {
          apiKey: process.env.ANTHROPIC_API_KEY,
        }),
        messages,
        system: context.systemPrompt,
      });

      // Convert to the data stream response that AI SDK expects
      const dataStreamResponse = result.toDataStreamResponse();
      
      // Copy headers and stream the response
      dataStreamResponse.headers.forEach((value, key) => {
        res.setHeader(key, value);
      });
      
      if (dataStreamResponse.body) {
        const reader = dataStreamResponse.body.getReader();
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            res.write(value);
          }
        } finally {
          reader.releaseLock();
        }
      }
      
      res.end();
    } catch (error) {
      console.error('Chat API error:', error);
      res.status(500).json({ error: 'Failed to process chat request' });
    }
  });

  // Authentication routes
  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = loginSchema.parse(req.body);
      
      const user = await storage.getUserByUsername(username);
      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Simple password check (in production, use bcrypt)
      if (password !== user.password) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      res.json({ 
        user: { 
          id: user.id, 
          username: user.username, 
          email: user.email, 
          role: user.role 
        } 
      });
    } catch (error) {
      res.status(400).json({ message: "Invalid request data" });
    }
  });

  app.post("/api/auth/logout", (req, res) => {
    res.json({ message: "Logged out successfully" });
  });

  // Company directory listing route
  app.get("/api/companies/list", async (req, res) => {
    try {
      const companiesDir = path.join(__dirname, '../client/public/data/companies');
      
      // Check if the companies directory exists
      if (!fs.existsSync(companiesDir)) {
        return res.json([]);
      }
      
      const directories = fs.readdirSync(companiesDir)
        .filter(item => {
          const fullPath = path.join(companiesDir, item);
          return fs.statSync(fullPath).isDirectory();
        })
        .filter(dir => {
          // Only include directories that have an executive-summary.json file
          const summaryPath = path.join(companiesDir, dir, 'executive-summary.json');
          return fs.existsSync(summaryPath);
        });
      
      res.json(directories);
    } catch (error) {
      console.error('Error listing companies:', error);
      res.status(500).json({ error: 'Cannot read companies directory' });
    }
  });

  // Company routes
  app.get("/api/companies", async (req, res) => {
    try {
      const companies = await storage.getCompanies();
      res.json(companies);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch companies" });
    }
  });

  app.get("/api/companies/:symbol", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      res.json(company);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch company" });
    }
  });

  // KPI metrics routes
  app.get("/api/companies/:symbol/kpis", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      const kpis = await storage.getKpiMetrics(company.id);
      res.json(kpis);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch KPI metrics" });
    }
  });

  // Performance data routes
  app.get("/api/companies/:symbol/performance", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      const performance = await storage.getPerformanceData(company.id);
      res.json(performance);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch performance data" });
    }
  });

  // Strategic insights routes
  app.get("/api/companies/:symbol/insights", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      const insights = await storage.getStrategicInsights(company.id);
      res.json(insights);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch strategic insights" });
    }
  });

  // Execution priorities routes
  app.get("/api/companies/:symbol/priorities", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      const priorities = await storage.getExecutionPriorities(company.id);
      res.json(priorities);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch execution priorities" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
