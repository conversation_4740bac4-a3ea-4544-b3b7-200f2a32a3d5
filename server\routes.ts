import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { loginSchema } from "@shared/schema";
import bcrypt from "bcrypt";

export async function registerRoutes(app: Express): Promise<Server> {
  // Authentication routes
  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = loginSchema.parse(req.body);
      
      const user = await storage.getUserByUsername(username);
      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Simple password check (in production, use bcrypt)
      if (password !== user.password) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      res.json({ 
        user: { 
          id: user.id, 
          username: user.username, 
          email: user.email, 
          role: user.role 
        } 
      });
    } catch (error) {
      res.status(400).json({ message: "Invalid request data" });
    }
  });

  app.post("/api/auth/logout", (req, res) => {
    res.json({ message: "Logged out successfully" });
  });

  // Company routes
  app.get("/api/companies", async (req, res) => {
    try {
      const companies = await storage.getCompanies();
      res.json(companies);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch companies" });
    }
  });

  app.get("/api/companies/:symbol", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      res.json(company);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch company" });
    }
  });

  // KPI metrics routes
  app.get("/api/companies/:symbol/kpis", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      const kpis = await storage.getKpiMetrics(company.id);
      res.json(kpis);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch KPI metrics" });
    }
  });

  // Performance data routes
  app.get("/api/companies/:symbol/performance", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      const performance = await storage.getPerformanceData(company.id);
      res.json(performance);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch performance data" });
    }
  });

  // Strategic insights routes
  app.get("/api/companies/:symbol/insights", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      const insights = await storage.getStrategicInsights(company.id);
      res.json(insights);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch strategic insights" });
    }
  });

  // Execution priorities routes
  app.get("/api/companies/:symbol/priorities", async (req, res) => {
    try {
      const { symbol } = req.params;
      const company = await storage.getCompany(symbol);
      
      if (!company) {
        return res.status(404).json({ message: "Company not found" });
      }
      
      const priorities = await storage.getExecutionPriorities(company.id);
      res.json(priorities);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch execution priorities" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
