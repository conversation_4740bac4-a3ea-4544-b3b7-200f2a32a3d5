import { useQuery } from "@tanstack/react-query";
import { Sidebar } from "@/components/layout/sidebar";
import { Header } from "@/components/layout/header";
import { KpiCard } from "@/components/dashboard/kpi-card";
import { PerformanceChart } from "@/components/dashboard/performance-chart";
import { PerformanceChartUnified } from "@/components/dashboard/performance-chart-unified";
import { JoyceChat } from "@/components/joyce-chat";
import { DollarSign, Phone, TrendingUp, Percent, Signal, Users } from "lucide-react";
import { KpiMetric, PerformanceData } from "@shared/schema";

export default function Dashboard() {
  const { data: kpis, isLoading: kpisLoading } = useQuery<KpiMetric[]>({
    queryKey: ["/api/companies/VZ/kpis"],
  });

  const { data: performance, isLoading: performanceLoading } = useQuery<PerformanceData[]>({
    queryKey: ["/api/companies/VZ/performance"],
  });

  const formatKpiValue = (kpi: KpiMetric) => {
    const current = parseFloat(kpi.value);
    const previous = kpi.previousValue ? parseFloat(kpi.previousValue) : current;
    const change = previous !== 0 ? ((current - previous) / previous) * 100 : 0;
    
    return {
      value: kpi.value,
      change: parseFloat(change.toFixed(1)),
    };
  };

  const chartData = performance?.map(p => ({
    period: p.period,
    revenue: parseFloat(p.revenue || "0"),
    freeCashFlow: parseFloat(p.freeCashFlow || "0"),
    churnRate: parseFloat(p.churnRate || "0"),
  })) || [];

  if (kpisLoading || performanceLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  const serviceRevenue = kpis?.find(k => k.metricName === "Service Revenue");
  const postpaidAdds = kpis?.find(k => k.metricName === "Postpaid Phone Net Adds");
  const freeCashFlow = kpis?.find(k => k.metricName === "Free Cash Flow");
  const churnRate = kpis?.find(k => k.metricName === "Churn Rate");
  const arpu = kpis?.find(k => k.metricName === "ARPU");
  const coverage = kpis?.find(k => k.metricName === "5G Coverage");

  return (
    <div className="min-h-screen flex bg-gray-50">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Header 
          title="Digital Mirror - Strategic Cockpit"
          subtitle="Real-time executive dashboard with key performance indicators"
        />
        
        <div className="flex-1 p-6 space-y-8">
          {/* Joyce AI Assistant */}
          <section>
            <JoyceChat />
          </section>

          {/* Key Performance Indicators */}
          <section>
            <h2 className="text-xl font-bold text-gray-900 mb-6">Key Performance Indicators</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {serviceRevenue && (
                <KpiCard
                  title="Service Revenue"
                  value={serviceRevenue.value}
                  unit="B"
                  icon={DollarSign}
                  color="green"
                  {...formatKpiValue(serviceRevenue)}
                  changeLabel="YoY"
                />
              )}
              {postpaidAdds && (
                <KpiCard
                  title="Postpaid Phone Net Adds"
                  value={postpaidAdds.value}
                  unit="K"
                  icon={Phone}
                  color="blue"
                  {...formatKpiValue(postpaidAdds)}
                  changeLabel="QoQ"
                />
              )}
              {freeCashFlow && (
                <KpiCard
                  title="Free Cash Flow"
                  value={freeCashFlow.value}
                  unit="B"
                  icon={TrendingUp}
                  color="green"
                  {...formatKpiValue(freeCashFlow)}
                  changeLabel="YoY"
                />
              )}
              {churnRate && (
                <KpiCard
                  title="Churn Rate"
                  value={churnRate.value}
                  unit="%"
                  icon={Percent}
                  color="red"
                  {...formatKpiValue(churnRate)}
                  changeLabel="QoQ"
                />
              )}
              {arpu && (
                <KpiCard
                  title="ARPU"
                  value={arpu.value}
                  unit="$"
                  icon={DollarSign}
                  color="green"
                  {...formatKpiValue(arpu)}
                  changeLabel="QoQ"
                />
              )}
              {coverage && (
                <KpiCard
                  title="5G Coverage"
                  value={coverage.value}
                  unit="M people"
                  icon={Signal}
                  color="blue"
                  {...formatKpiValue(coverage)}
                  changeLabel="QoQ"
                />
              )}
            </div>
          </section>

          {/* Performance Charts */}
          <section>
            <h2 className="text-xl font-bold text-gray-900 mb-6">Performance Trends</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <PerformanceChartUnified
                title="Revenue Trend"
                data={chartData}
                dataKey="revenue"
                color="#10B981"
              />
              <PerformanceChart
                title="Free Cash Flow Trend"
                data={chartData}
                dataKey="freeCashFlow"
                color="#3B82F6"
              />
              <PerformanceChart
                title="Churn Rate Trend"
                data={chartData}
                dataKey="churnRate"
                color="#EF4444"
              />
              <PerformanceChart
                title="Customer Growth"
                data={chartData}
                dataKey="customerCount"
                color="#F59E0B"
                type="bar"
              />
            </div>
          </section>

          {/* Quick Insights */}
          <section>
            <h2 className="text-xl font-bold text-gray-900 mb-6">Strategic Insights</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <h3 className="font-semibold text-gray-900">Growth Drivers</h3>
                </div>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li>• 5G network expansion driving customer acquisition</li>
                  <li>• Strong postpaid phone net additions</li>
                  <li>• Improved customer retention strategies</li>
                  <li>• Premium service offerings gaining traction</li>
                </ul>
              </div>
              
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
                  <h3 className="font-semibold text-gray-900">Focus Areas</h3>
                </div>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li>• Continue 5G network rollout acceleration</li>
                  <li>• Enhance digital customer experience</li>
                  <li>• Optimize operational efficiency</li>
                  <li>• Strengthen enterprise service offerings</li>
                </ul>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
