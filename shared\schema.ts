import { pgTable, text, serial, integer, boolean, timestamp, decimal, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email").notNull(),
  role: text("role").notNull().default("analyst"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const companies = pgTable("companies", {
  id: serial("id").primaryKey(),
  symbol: text("symbol").notNull().unique(),
  name: text("name").notNull(),
  sector: text("sector").notNull(),
  marketCap: decimal("market_cap"),
  description: text("description"),
});

export const kpiMetrics = pgTable("kpi_metrics", {
  id: serial("id").primaryKey(),
  companyId: integer("company_id").notNull(),
  metricName: text("metric_name").notNull(),
  value: decimal("value").notNull(),
  previousValue: decimal("previous_value"),
  period: text("period").notNull(),
  unit: text("unit").notNull(),
  category: text("category").notNull(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const performanceData = pgTable("performance_data", {
  id: serial("id").primaryKey(),
  companyId: integer("company_id").notNull(),
  period: text("period").notNull(),
  revenue: decimal("revenue"),
  netIncome: decimal("net_income"),
  freeCashFlow: decimal("free_cash_flow"),
  churnRate: decimal("churn_rate"),
  customerCount: integer("customer_count"),
  arpu: decimal("arpu"),
  data: jsonb("data"),
});

export const strategicInsights = pgTable("strategic_insights", {
  id: serial("id").primaryKey(),
  companyId: integer("company_id").notNull(),
  category: text("category").notNull(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  priority: text("priority").notNull(),
  status: text("status").notNull(),
  assignedTo: text("assigned_to"),
  dueDate: timestamp("due_date"),
});

export const executionPriorities = pgTable("execution_priorities", {
  id: serial("id").primaryKey(),
  companyId: integer("company_id").notNull(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  priority: text("priority").notNull(),
  category: text("category").notNull(),
  progress: integer("progress").default(0),
  owner: text("owner"),
  timeline: text("timeline"),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  email: true,
});

export const loginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

export const insertCompanySchema = createInsertSchema(companies).omit({
  id: true,
});

export const insertKpiMetricSchema = createInsertSchema(kpiMetrics).omit({
  id: true,
  updatedAt: true,
});

export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type LoginData = z.infer<typeof loginSchema>;
export type Company = typeof companies.$inferSelect;
export type InsertCompany = z.infer<typeof insertCompanySchema>;
export type KpiMetric = typeof kpiMetrics.$inferSelect;
export type PerformanceData = typeof performanceData.$inferSelect;
export type StrategicInsight = typeof strategicInsights.$inferSelect;
export type ExecutionPriority = typeof executionPriorities.$inferSelect;
