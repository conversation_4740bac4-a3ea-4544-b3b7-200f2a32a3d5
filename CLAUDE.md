# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Rejoyce is a full-stack enterprise analytics platform built with React, Express, and PostgreSQL. It provides strategic insights, KPI monitoring, and executive dashboards for telecommunications companies (primarily focused on Verizon data). The platform includes an SEI (Strategic Enterprise Intelligence) report system and a conversational AI agent called <PERSON>.

## Architecture

### Full-Stack Monorepo Structure
- **Client**: React SPA with Vite (`client/src/`)
- **Server**: Express API server (`server/`)
- **Shared**: Common schemas and types (`shared/`)
- **Database**: PostgreSQL with Drizzle ORM

### Key Technologies
- **Frontend**: React 18, TypeScript, Tailwind CSS, shadcn/ui, Wouter (routing), React Query
- **Backend**: Express.js, Drizzle ORM, PostgreSQL
- **Build Tools**: Vite, esbuild, TypeScript
- **UI Components**: Radix UI primitives with shadcn/ui styling

## Development Commands

```bash
# Start development server (runs both client and server)
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Type checking
npm run check

# Database operations
npm run db:push    # Push schema changes to database
```

## Database Schema

Located in `shared/schema.ts` with the following main entities:
- **users**: Authentication and authorization
- **companies**: Company information and metadata
- **kpiMetrics**: Key performance indicators with historical data
- **performanceData**: Financial and operational metrics
- **strategicInsights**: Strategic recommendations and insights
- **executionPriorities**: Action items and roadmap priorities

## API Architecture

RESTful API endpoints in `server/routes.ts`:
- Authentication: `/api/auth/login`, `/api/auth/logout`
- Companies: `/api/companies`, `/api/companies/:symbol`
- KPIs: `/api/companies/:symbol/kpis`
- Performance: `/api/companies/:symbol/performance`
- Insights: `/api/companies/:symbol/insights`
- Priorities: `/api/companies/:symbol/priorities`

## Frontend Architecture

### Component Structure
- **Layout**: Header, Sidebar components in `client/src/components/layout/`
- **Dashboard**: KPI cards, performance charts in `client/src/components/dashboard/`
- **SEI Report**: Multi-section executive report in `client/src/components/sei-report/`
- **UI Components**: shadcn/ui components in `client/src/ui/`

### Routing
Uses Wouter for client-side routing with protected routes:
- `/` - Dashboard (default)
- `/sei-report` - Strategic Enterprise Intelligence report
- `/joyce-agent` - AI conversational agent
- `/login` - Authentication page

### State Management
- **React Query**: Server state and API caching
- **Auth Context**: User authentication state
- **Custom Hooks**: `use-auth`, `use-mobile`, `use-toast`

## Path Aliases

TypeScript and Vite configured with path aliases:
- `@/*` → `client/src/*`
- `@shared/*` → `shared/*`
- `@assets/*` → `attached_assets/*`

## Database Connection

Requires `DATABASE_URL` environment variable for PostgreSQL connection. Uses Drizzle ORM with connection pooling via `@neondatabase/serverless`.

## Development Notes

### Server Setup
- Development server runs on port 5000 (hardcoded)
- Client dev server proxied through Vite
- API logging middleware captures request/response data
- Uses Express sessions for authentication (currently simplified)

### UI Styling
- Tailwind CSS with custom design system
- CSS variables for theming support
- shadcn/ui components follow "new-york" style variant
- Dark mode support configured

### Component Patterns
- Functional components with TypeScript
- React Query for data fetching
- Controlled forms with validation
- Responsive design with mobile-first approach

## Security Considerations

- Authentication currently uses simple password comparison (needs bcrypt implementation)
- No CSRF protection implemented
- Environment variables required for database connection
- Input validation using Zod schemas