import { 
  users, companies, kpiMetrics, performanceData, strategicInsights, executionPriorities,
  type User, type InsertUser, type Company, type KpiMetric, type PerformanceData,
  type StrategicInsight, type ExecutionPriority 
} from "@shared/schema";

export interface IStorage {
  // User management
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Company management
  getCompanies(): Promise<Company[]>;
  getCompany(symbol: string): Promise<Company | undefined>;
  
  // KPI metrics
  getKpiMetrics(companyId: number): Promise<KpiMetric[]>;
  
  // Performance data
  getPerformanceData(companyId: number): Promise<PerformanceData[]>;
  
  // Strategic insights
  getStrategicInsights(companyId: number): Promise<StrategicInsight[]>;
  
  // Execution priorities
  getExecutionPriorities(companyId: number): Promise<ExecutionPriority[]>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private companies: Map<number, Company>;
  private kpiMetrics: Map<number, KpiMetric>;
  private performanceData: Map<number, PerformanceData>;
  private strategicInsights: Map<number, StrategicInsight>;
  private executionPriorities: Map<number, ExecutionPriority>;
  private currentId: number;

  constructor() {
    this.users = new Map();
    this.companies = new Map();
    this.kpiMetrics = new Map();
    this.performanceData = new Map();
    this.strategicInsights = new Map();
    this.executionPriorities = new Map();
    this.currentId = 1;
    this.seedData();
  }

  private seedData() {
    // Seed demo user
    this.createUser({
      username: "analyst",
      password: "password",
      email: "<EMAIL>"
    });

    // Seed Verizon company data
    const verizon: Company = {
      id: 1,
      symbol: "VZ",
      name: "Verizon Communications Inc.",
      sector: "Telecommunications",
      marketCap: "150000000000",
      description: "Verizon delivers a broad portfolio of telecommunications products and services, including wireless connectivity, broadband internet, and solutions for both consumers and businesses."
    };
    this.companies.set(1, verizon);

    // Seed KPI metrics for Verizon
    const kpis: Omit<KpiMetric, 'id'>[] = [
      { companyId: 1, metricName: "Service Revenue", value: "28.6", previousValue: "28.0", period: "Q2 2023", unit: "B", category: "financial", updatedAt: new Date() },
      { companyId: 1, metricName: "Postpaid Phone Net Adds", value: "420", previousValue: "400", period: "Q2 2023", unit: "K", category: "operational", updatedAt: new Date() },
      { companyId: 1, metricName: "Free Cash Flow", value: "7.1", previousValue: "6.3", period: "Q2 2023", unit: "B", category: "financial", updatedAt: new Date() },
      { companyId: 1, metricName: "Churn Rate", value: "0.83", previousValue: "0.88", period: "Q2 2023", unit: "%", category: "operational", updatedAt: new Date() },
      { companyId: 1, metricName: "ARPU", value: "124.50", previousValue: "123.80", period: "Q2 2023", unit: "$", category: "financial", updatedAt: new Date() },
      { companyId: 1, metricName: "5G Coverage", value: "230", previousValue: "220", period: "Q2 2023", unit: "M people", category: "operational", updatedAt: new Date() }
    ];

    kpis.forEach(kpi => {
      this.kpiMetrics.set(this.currentId, { ...kpi, id: this.currentId });
      this.currentId++;
    });

    // Seed performance data
    const perfData: Omit<PerformanceData, 'id'>[] = [
      { companyId: 1, period: "Q1 2023", revenue: "32800", netIncome: "5200", freeCashFlow: "6800", churnRate: "0.88", customerCount: 92000000, arpu: "123.80", data: {} },
      { companyId: 1, period: "Q2 2023", revenue: "33300", netIncome: "5400", freeCashFlow: "7100", churnRate: "0.83", customerCount: 93200000, arpu: "124.50", data: {} }
    ];

    perfData.forEach(data => {
      this.performanceData.set(this.currentId, { ...data, id: this.currentId });
      this.currentId++;
    });

    // Seed strategic insights
    const insights: Omit<StrategicInsight, 'id'>[] = [
      {
        companyId: 1,
        category: "Growth Trajectory",
        title: "5G Network Leadership",
        description: "Consumer Wireless revenue has shown steady growth, supported by increased postpaid phone net additions and stable ARPUs.",
        priority: "high",
        status: "active",
        assignedTo: "Network Team",
        dueDate: new Date("2023-12-31")
      },
      {
        companyId: 1,
        category: "Areas for Improvement",
        title: "Cost Management Focus",
        description: "Reducing churn remains a focus, as competitive pressures persist in the wireless market.",
        priority: "medium",
        status: "in_progress",
        assignedTo: "Customer Experience Team",
        dueDate: new Date("2023-09-30")
      },
      {
        companyId: 1,
        category: "Key Performance Drivers",
        title: "Digital Transformation",
        description: "Key drivers include network reliability, 5G adoption, and effective promotional strategies.",
        priority: "high",
        status: "active",
        assignedTo: "Technology Team",
        dueDate: new Date("2024-03-31")
      }
    ];

    insights.forEach(insight => {
      this.strategicInsights.set(this.currentId, { ...insight, id: this.currentId });
      this.currentId++;
    });

    // Seed execution priorities
    const priorities: Omit<ExecutionPriority, 'id'>[] = [
      {
        companyId: 1,
        title: "Accelerate 5G Network Rollout",
        description: "Expand coverage and capacity to maintain competitive edge",
        priority: "HIGH",
        category: "Network Infrastructure",
        progress: 75,
        owner: "Network Operations",
        timeline: "Q4 2023"
      },
      {
        companyId: 1,
        title: "Enhance Customer Retention",
        description: "Reduce churn through improved service and loyalty programs",
        priority: "HIGH",
        category: "Customer Experience",
        progress: 60,
        owner: "Customer Experience Team",
        timeline: "Q3 2023"
      },
      {
        companyId: 1,
        title: "Optimize Capital Allocation",
        description: "Balance network investment with debt reduction and shareholder returns",
        priority: "MEDIUM",
        category: "Financial Management",
        progress: 45,
        owner: "Finance Team",
        timeline: "Q4 2023"
      },
      {
        companyId: 1,
        title: "Advance Digital Transformation",
        description: "Leverage automation and AI to streamline operations",
        priority: "MEDIUM",
        category: "Technology",
        progress: 30,
        owner: "Technology Team",
        timeline: "Q1 2024"
      }
    ];

    priorities.forEach(priority => {
      this.executionPriorities.set(this.currentId, { ...priority, id: this.currentId });
      this.currentId++;
    });
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.username === username);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = { 
      ...insertUser, 
      id, 
      role: "analyst",
      createdAt: new Date()
    };
    this.users.set(id, user);
    return user;
  }

  async getCompanies(): Promise<Company[]> {
    return Array.from(this.companies.values());
  }

  async getCompany(symbol: string): Promise<Company | undefined> {
    return Array.from(this.companies.values()).find(company => company.symbol === symbol);
  }

  async getKpiMetrics(companyId: number): Promise<KpiMetric[]> {
    return Array.from(this.kpiMetrics.values()).filter(metric => metric.companyId === companyId);
  }

  async getPerformanceData(companyId: number): Promise<PerformanceData[]> {
    return Array.from(this.performanceData.values()).filter(data => data.companyId === companyId);
  }

  async getStrategicInsights(companyId: number): Promise<StrategicInsight[]> {
    return Array.from(this.strategicInsights.values()).filter(insight => insight.companyId === companyId);
  }

  async getExecutionPriorities(companyId: number): Promise<ExecutionPriority[]> {
    return Array.from(this.executionPriorities.values()).filter(priority => priority.companyId === companyId);
  }
}

export const storage = new MemStorage();
