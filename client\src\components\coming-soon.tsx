import { Construction, Clock, ChevronRight } from "lucide-react";
import { Card, CardContent } from "@/ui/card";
import { Button } from "@/ui/button";

interface ComingSoonProps {
  title: string;
  description?: string;
  estimatedDate?: string;
}

export function ComingSoon({ 
  title, 
  description = "This feature is currently under development and will be available soon.",
  estimatedDate 
}: ComingSoonProps) {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-6">
      <Card className="max-w-md w-full">
        <CardContent className="pt-6 text-center">
          <div className="mb-6">
            <Construction className="w-16 h-16 text-orange-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">{title}</h1>
            <p className="text-gray-600">{description}</p>
          </div>
          
          {estimatedDate && (
            <div className="flex items-center justify-center space-x-2 text-sm text-gray-500 mb-6">
              <Clock className="w-4 h-4" />
              <span>Expected: {estimatedDate}</span>
            </div>
          )}
          
          <div className="space-y-3">
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => window.history.back()}
            >
              Go Back
            </Button>
            
            <Button 
              className="w-full"
              onClick={() => window.location.href = '/'}
            >
              <span>Go to Home</span>
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}