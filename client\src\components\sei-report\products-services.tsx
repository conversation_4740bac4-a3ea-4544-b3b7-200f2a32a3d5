import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts";
import { ChevronRight, ChevronLeft } from "lucide-react";
import { Button } from "@/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/ui/select";
import { KpiMetric } from "@shared/schema";

interface ProductsServicesProps {
  onNextSection: () => void;
  onPrevSection: () => void;
}

export function ProductsServices({ onNextSection, onPrevSection }: ProductsServicesProps) {
  const { data: kpis } = useQuery<KpiMetric[]>({
    queryKey: ["/api/companies/VZ/kpis"],
  });

  const quarterlyData = [
    { quarter: "Q2 2023", value: 95 },
    { quarter: "Q1 2024", value: 97 },
    { quarter: "Q2 2024", value: 98 },
    { quarter: "Q3 2024", value: 96 },
    { quarter: "Q4 2024", value: 94 },
  ];

  const serviceRevenue = kpis?.find(k => k.metricName === "Service Revenue");
  const postpaidAdds = kpis?.find(k => k.metricName === "Postpaid Phone Net Adds");
  const churnRate = kpis?.find(k => k.metricName === "Churn Rate");
  const arpu = kpis?.find(k => k.metricName === "ARPU");

  return (
    <div className="p-6 space-y-8">
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-4">Products & Services Analysis</h2>
        <p className="text-gray-700 mb-6">
          Verizon (VZ) delivers a broad portfolio of telecommunications products and services, including wireless connectivity, broadband internet, and solutions for both 
          consumers and businesses. The company's offerings are structured around key segments that address the diverse needs of its customer base.
        </p>

        {/* Product Segment Selector */}
        <div className="mb-6">
          <div className="text-sm font-medium text-gray-700 mb-2">Select a Product Segment</div>
          <Select defaultValue="consumer-wireless">
            <SelectTrigger className="w-64">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="consumer-wireless">Consumer Wireless</SelectItem>
              <SelectItem value="business-wireless">Business Wireless</SelectItem>
              <SelectItem value="broadband">Broadband</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Consumer Wireless Key Metrics */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Consumer Wireless Key Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-6">
              {serviceRevenue && (
                <div>
                  <div className="text-sm text-gray-500 mb-1">Revenue</div>
                  <div className="text-2xl font-bold text-gray-900">${serviceRevenue.value}B</div>
                  <div className="text-sm text-green-600">****%</div>
                </div>
              )}
              {postpaidAdds && (
                <div>
                  <div className="text-sm text-gray-500 mb-1">Postpaid Phone Net Adds</div>
                  <div className="text-2xl font-bold text-gray-900">{postpaidAdds.value}K</div>
                  <div className="text-sm text-red-600">+14K</div>
                </div>
              )}
              {churnRate && (
                <div>
                  <div className="text-sm text-gray-500 mb-1">Churn Rate</div>
                  <div className="text-2xl font-bold text-gray-900">{churnRate.value}%</div>
                  <div className="text-sm text-red-600">-0.02pts</div>
                </div>
              )}
              {arpu && (
                <div>
                  <div className="text-sm text-gray-500 mb-1">ARPU</div>
                  <div className="text-2xl font-bold text-gray-900">${arpu.value}</div>
                  <div className="text-sm text-green-600">+0.6%</div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Quarterly Performance Trends */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Quarterly Performance Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={400}>
              <LineChart data={quarterlyData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis dataKey="quarter" axisLine={false} tickLine={false} />
                <YAxis 
                  domain={[0, 100]} 
                  axisLine={false} 
                  tickLine={false} 
                />
                <Line 
                  type="monotone" 
                  dataKey="value" 
                  stroke="#3B82F6" 
                  strokeWidth={3}
                  dot={{ fill: "#3B82F6", strokeWidth: 2, r: 5 }}
                />
                <Line 
                  type="monotone" 
                  dataKey="value" 
                  stroke="#10B981" 
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={{ fill: "#10B981", strokeWidth: 2, r: 4 }}
                />
                <Line 
                  type="monotone" 
                  dataKey="value" 
                  stroke="#EF4444" 
                  strokeWidth={2}
                  dot={{ fill: "#EF4444", strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Performance Analysis Tabs */}
        <div className="bg-white border border-gray-200 rounded-lg mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button className="py-4 border-b-2 border-blue-600 text-blue-600 font-medium">
                Performance Analysis
              </button>
              <button className="py-4 border-b-2 border-transparent text-gray-600 hover:text-gray-900">
                Competitive Positioning
              </button>
              <button className="py-4 border-b-2 border-transparent text-gray-600 hover:text-gray-900">
                Execution Insights
              </button>
            </nav>
          </div>
          <div className="p-6">
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Growth Trajectory</h3>
                  <p className="text-sm text-gray-600">Consumer Wireless revenue has shown steady growth, supported by increased postpaid phone net additions and stable ARPUs.</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
                  <div className="w-3 h-3 bg-yellow-600 rounded-full"></div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Areas for Improvement</h3>
                  <p className="text-sm text-gray-600">Reducing churn remains a focus, as competitive pressures persist in the wireless market.</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Key Performance Drivers</h3>
                  <p className="text-sm text-gray-600">Key drivers include network reliability, 5G adoption, and effective promotional strategies.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-8 border-t border-gray-200">
        <Button variant="ghost" onClick={onPrevSection} className="flex items-center space-x-2">
          <ChevronLeft className="w-4 h-4" />
          <span>Previous Section</span>
        </Button>
        <Button onClick={onNextSection} className="flex items-center space-x-2">
          <span>Next Section</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
