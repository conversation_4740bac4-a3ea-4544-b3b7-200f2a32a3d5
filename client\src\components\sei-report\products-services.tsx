import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/ui/card";
import { ChevronRight, ChevronLeft, Lightbulb, TrendingUp, Target } from "lucide-react";
import { <PERSON><PERSON> } from "@/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/ui/select";
import { Badge } from "@/ui/badge";
import { useCompanyData } from "@/hooks/use-company-data";
import { useCompany } from "@/contexts/company-context";
import { DataVisualization, generateChartConfig } from "@/components/ui/data-visualization";

interface ProductsServicesProps {
  onNextSection: () => void;
  onPrevSection: () => void;
  activeJourneyStage?: string;
}

export function ProductsServices({ onNextSection, onPrevSection, activeJourneyStage = "summary" }: ProductsServicesProps) {
  const { selectedCompany } = useCompany();
  const { data: contentData, loading: contentLoading } = useCompanyData(selectedCompany, "products-services");
  const [activeSegment, setActiveSegment] = useState("");
  const [activeCategory, setActiveCategory] = useState("performance-analysis");
  
  // Reset and set the initial segment when company or data changes
  useEffect(() => {
    if (contentData && contentData.productSegments?.length > 0) {
      setActiveSegment(contentData.productSegments[0].id);
    }
  }, [contentData, selectedCompany]);
  
  if (contentLoading) {
    return <div className="p-6">Loading...</div>;
  }

  if (!contentData) {
    return <div className="p-6">Failed to load content data</div>;
  }

  // Show loading while activeSegment is being set
  if (!activeSegment) {
    return <div className="p-6">Loading...</div>;
  }

  const currentSegmentData = contentData.segmentData?.[activeSegment];
  if (!currentSegmentData) {
    const availableSegments = Object.keys(contentData.segmentData || {});
    return (
      <div className="p-6">
        <p>Segment data not found for: {activeSegment}</p>
        <p>Available segments: {availableSegments.join(", ")}</p>
        <p>Product segments from config: {contentData.productSegments?.map(s => s.id).join(", ")}</p>
      </div>
    );
  }

  const currentCategoryData = currentSegmentData[activeCategory.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase())];
  const currentJourneyStageData = currentSegmentData.journeyStages?.[activeJourneyStage];

  const renderSummaryContent = () => {
    return (
      <div className="space-y-8">
        {/* Product Segment Selector */}
        <div className="mb-6">
          <div className="text-sm font-medium text-gray-700 mb-2">Select a Product Segment</div>
          <Select value={activeSegment} onValueChange={setActiveSegment}>
            <SelectTrigger className="w-64">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {contentData.productSegments.map((segment: any) => (
                <SelectItem key={segment.id} value={segment.id}>{segment.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Key Metrics */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>{contentData.productSegments.find((s: any) => s.id === activeSegment)?.label} Key Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-6">
              {currentSegmentData.performanceAnalysis.keyMetrics.map((metric: any, index: number) => (
                <div key={index}>
                  <div className="text-sm text-gray-500 mb-1">{metric.name}</div>
                  <div className="text-2xl font-bold text-gray-900">{metric.value}</div>
                  <div className={`text-sm ${
                    metric.trend === 'positive' ? 'text-green-600' : 
                    metric.trend === 'negative' ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {metric.description}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quarterly Performance Trends */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Quarterly Performance Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <DataVisualization
              type="line"
              data={contentData.chartData.quarterlyPerformance}
              config={generateChartConfig(
                contentData.chartData.quarterlyPerformance,
                ["value"],
                { value: "Performance" },
                { value: "#3B82F6" }
              )}
              xKey="quarter"
              yKey="value"
              height={400}
              tooltip={{ enabled: true }}
              axes={{
                yAxis: {
                  domain: [0, 100]
                }
              }}
            />
          </CardContent>
        </Card>

        {/* Analysis Category Tabs */}
        <div className="bg-white border border-gray-200 rounded-lg mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {contentData.analysisCategories.map((category: any) => (
                <button 
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`py-4 border-b-2 font-medium ${
                    activeCategory === category.id
                      ? "border-blue-600 text-blue-600"
                      : "border-transparent text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {category.label}
                </button>
              ))}
            </nav>
          </div>
          <div className="p-6">
            {activeCategory === 'performance-analysis' && (
              <div className="space-y-6">
                <div className="mb-6">
                  <div className="flex items-center space-x-2 mb-4">
                    <Lightbulb className="w-5 h-5 text-blue-600" />
                    <h3 className="font-semibold text-gray-900">Key Performance Insights</h3>
                  </div>
                  <ul className="space-y-3">
                    {currentCategoryData.insights.map((insight: string, index: number) => (
                      <li key={index} className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                        <span className="text-sm text-gray-700">{insight}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
            {activeCategory === 'competitive-positioning' && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-8">
                  <div>
                    <div className="flex items-center space-x-2 mb-4">
                      <Target className="w-5 h-5 text-blue-600" />
                      <h3 className="font-semibold text-gray-900">Market Position</h3>
                    </div>
                    <div className="mb-4">
                      <Badge variant="secondary">{currentCategoryData.marketPosition}</Badge>
                      <span className="ml-2 text-sm text-gray-600">Market Share: {currentCategoryData.marketShare}</span>
                    </div>
                    <div className="mb-6">
                      <h4 className="font-medium text-gray-900 mb-3">Strengths</h4>
                      <ul className="space-y-2">
                        {currentCategoryData.strengths.map((strength: string, index: number) => (
                          <li key={index} className="flex items-start space-x-3">
                            <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                            <span className="text-sm text-gray-700">{strength}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center space-x-2 mb-4">
                      <TrendingUp className="w-5 h-5 text-blue-600" />
                      <h3 className="font-semibold text-gray-900">Challenges</h3>
                    </div>
                    <ul className="space-y-3">
                      {currentCategoryData.challenges.map((challenge: string, index: number) => (
                        <li key={index} className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-yellow-600 rounded-full mt-2"></div>
                          <span className="text-sm text-gray-700">{challenge}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}
            {activeCategory === 'execution-insights' && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-8">
                  <div>
                    <div className="flex items-center space-x-2 mb-4">
                      <Target className="w-5 h-5 text-blue-600" />
                      <h3 className="font-semibold text-gray-900">Strategic Priorities</h3>
                    </div>
                    <ul className="space-y-3">
                      {currentCategoryData.strategicPriorities.map((priority: string, index: number) => (
                        <li key={index} className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                          <span className="text-sm text-gray-700">{priority}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <div className="flex items-center space-x-2 mb-4">
                      <TrendingUp className="w-5 h-5 text-blue-600" />
                      <h3 className="font-semibold text-gray-900">Risk Factors</h3>
                    </div>
                    <ul className="space-y-3">
                      {currentCategoryData.riskFactors.map((risk: string, index: number) => (
                        <li key={index} className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-red-600 rounded-full mt-2"></div>
                          <span className="text-sm text-gray-700">{risk}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Strategic Action Items */}
        <Card>
          <CardHeader>
            <CardTitle>Strategic Action Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {contentData.strategicActionItems.map((item: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">{item.title}</h4>
                    <p className="text-sm text-gray-600">{item.description}</p>
                  </div>
                  <Badge variant={item.priority === 'HIGH' ? 'destructive' : 'secondary'}>{item.priority}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Joy Score Panel */}
        <Card>
          <CardHeader>
            <CardTitle>{contentData.joyScorePanel.title}</CardTitle>
            <p className="text-sm text-gray-600">{contentData.joyScorePanel.description}</p>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 px-3 font-medium text-gray-700">Stage</th>
                    <th className="text-left py-2 px-3 font-medium text-gray-700">Product A</th>
                    <th className="text-left py-2 px-3 font-medium text-gray-700">Product B</th>
                    <th className="text-left py-2 px-3 font-medium text-gray-700">Service X</th>
                    <th className="text-left py-2 px-3 font-medium text-gray-700">Benchmark</th>
                    <th className="text-left py-2 px-3 font-medium text-gray-700">Insight</th>
                  </tr>
                </thead>
                <tbody>
                  {contentData.joyScorePanel.joyScoreData.map((row: any, index: number) => (
                    <tr key={index} className="border-b">
                      <td className="py-2 px-3 font-medium">{row.stage}</td>
                      <td className="py-2 px-3">{row.productA}</td>
                      <td className="py-2 px-3">{row.productB}</td>
                      <td className="py-2 px-3">{row.serviceX}</td>
                      <td className="py-2 px-3">{row.benchmark}</td>
                      <td className="py-2 px-3 text-sm text-gray-600">{row.insight}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Sentiment Analysis */}
        <Card>
          <CardHeader>
            <CardTitle>{contentData.sentimentAnalysis.title}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-green-600 mb-3">Positive Drivers</h4>
                <div className="flex flex-wrap gap-2">
                  {contentData.sentimentAnalysis.positiveDrivers.map((driver: string, index: number) => (
                    <Badge key={index} variant="secondary" className="bg-green-100 text-green-800">
                      {driver}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-medium text-red-600 mb-3">Negative Drivers</h4>
                <div className="flex flex-wrap gap-2">
                  {contentData.sentimentAnalysis.negativeDrivers.map((driver: string, index: number) => (
                    <Badge key={index} variant="secondary" className="bg-red-100 text-red-800">
                      {driver}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Plan & Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle>Action Plan & Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 px-3 font-medium text-gray-700">Recommendation</th>
                    <th className="text-left py-2 px-3 font-medium text-gray-700">Unit</th>
                    <th className="text-left py-2 px-3 font-medium text-gray-700">Stage</th>
                    <th className="text-left py-2 px-3 font-medium text-gray-700">NPV Impact ($K)</th>
                    <th className="text-left py-2 px-3 font-medium text-gray-700">Complexity</th>
                    <th className="text-left py-2 px-3 font-medium text-gray-700">Target Quarter</th>
                  </tr>
                </thead>
                <tbody>
                  {contentData.actionPlanRecommendations.map((rec: any, index: number) => (
                    <tr key={index} className="border-b">
                      <td className="py-2 px-3">{rec.recommendation}</td>
                      <td className="py-2 px-3">{rec.unit}</td>
                      <td className="py-2 px-3">{rec.stage}</td>
                      <td className="py-2 px-3">{rec.npvImpact}</td>
                      <td className="py-2 px-3">
                        <Badge variant={rec.complexity === 'High' ? 'destructive' : rec.complexity === 'Medium' ? 'default' : 'secondary'}>
                          {rec.complexity}
                        </Badge>
                      </td>
                      <td className="py-2 px-3">{rec.targetQuarter}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderJourneyStageContent = () => {
    if (!currentJourneyStageData) {
      return <div>No journey stage data available</div>;
    }

    return (
      <div className="space-y-6">
        {/* Inputs & Outputs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-blue-600" />
              <span>Inputs & Outputs</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-4">
              <div>
                <div className="text-sm font-medium text-gray-700">Input</div>
                <div className="text-lg font-semibold text-gray-900">{currentJourneyStageData.inputsOutputs.input}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-700">Output</div>
                <div className="text-lg font-semibold text-gray-900">{currentJourneyStageData.inputsOutputs.output}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-700">KPI</div>
                <div className="text-lg font-semibold text-gray-900">{currentJourneyStageData.inputsOutputs.kpi}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-700">Notes</div>
                <div className="text-sm text-gray-600">-</div>
              </div>
            </div>
            {currentJourneyStageData.inputsOutputs.additionalMetrics && (
              <div className="mt-4 grid grid-cols-2 gap-4">
                {currentJourneyStageData.inputsOutputs.additionalMetrics.map((metric: any, index: number) => (
                  <div key={index} className="flex justify-between">
                    <span className="text-sm text-gray-600">{metric.metric}</span>
                    <span className="text-sm font-medium text-gray-900">{metric.value}</span>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Organizational Maturity & Joy Score */}
        <div className="grid grid-cols-2 gap-6">
          <div>
            <div className="text-sm font-medium text-gray-700 mb-2">Organizational Maturity Level</div>
            <div className="text-gray-900">{currentJourneyStageData.organizationalMaturity}</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-700 mb-2">Joy Score</div>
            <div className="text-2xl font-bold text-blue-600">{currentJourneyStageData.joyScore}</div>
          </div>
        </div>

        <div className="text-sm text-gray-600">{currentJourneyStageData.selmExecution}</div>

        {/* Customer Verbatim Feedback */}
        <Card>
          <CardHeader>
            <CardTitle className="text-blue-600">Customer Verbatim Feedback</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {currentJourneyStageData.customerFeedback.map((feedback: string, index: number) => (
                <li key={index} className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-700">{feedback}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Strengths & Opportunities */}
        <div className="grid grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-green-600">Strengths</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {currentJourneyStageData.strengths.map((strength: string, index: number) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                    <span className="text-sm text-gray-700">{strength}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-blue-600">Opportunities</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {currentJourneyStageData.opportunities.map((opportunity: string, index: number) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                    <span className="text-sm text-gray-700">{opportunity}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-8">
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-4">{contentData.productsServicesOverview.title}</h2>
        <p className="text-gray-700 mb-6">
          {contentData.productsServicesOverview.description}
        </p>

        {/* Render content based on active journey stage */}
        {activeJourneyStage === "summary" ? renderSummaryContent() : renderJourneyStageContent()}
      </section>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-8 border-t border-gray-200">
        <Button variant="ghost" onClick={onPrevSection} className="flex items-center space-x-2">
          <ChevronLeft className="w-4 h-4" />
          <span>Previous Section</span>
        </Button>
        <Button onClick={onNextSection} className="flex items-center space-x-2">
          <span>Next Section</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}