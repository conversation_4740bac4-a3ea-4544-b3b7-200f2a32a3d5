import { DataVisualization, generateChartConfig } from "@/components/ui/data-visualization"

// Example data sets
const revenueData = [
  { period: "Q1", revenue: 85.2, target: 80.0 },
  { period: "Q2", revenue: 92.1, target: 85.0 },
  { period: "Q3", revenue: 78.9, target: 82.0 },
  { period: "Q4", revenue: 95.4, target: 90.0 },
]

const customerSegmentData = [
  { segment: "Enterprise", value: 45, customers: 1200 },
  { segment: "SMB", value: 30, customers: 3500 },
  { segment: "Consumer", value: 25, customers: 12000 },
]

const performanceData = [
  { month: "Jan", revenue: 82.5, churnRate: 2.1, newCustomers: 450 },
  { month: "Feb", revenue: 78.9, churnRate: 2.3, newCustomers: 380 },
  { month: "Mar", revenue: 91.2, churnRate: 1.8, newCustomers: 520 },
  { month: "Apr", revenue: 87.6, churnRate: 2.0, newCustomers: 470 },
  { month: "May", revenue: 93.1, churnRate: 1.9, newCustomers: 610 },
]

// Example usage components
export function DataVisualizationExamples() {
  return (
    <div className="space-y-8 p-6">
      <h1 className="text-3xl font-bold">Data Visualization Examples</h1>
      
      {/* Line Chart Example */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Line Chart</h2>
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-medium mb-4">Revenue vs Target</h3>
          <DataVisualization
            type="line"
            data={revenueData}
            config={generateChartConfig(
              revenueData,
              ["revenue", "target"],
              { revenue: "Revenue ($M)", target: "Target ($M)" },
              { revenue: "#10B981", target: "#3B82F6" }
            )}
            xKey="period"
            yKey={["revenue", "target"]}
            height={300}
            tooltip={{ enabled: true }}
            legend={{ enabled: true, position: "top" }}
          />
        </div>
      </section>

      {/* Bar Chart Example */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Bar Chart</h2>
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-medium mb-4">Monthly New Customers</h3>
          <DataVisualization
            type="bar"
            data={performanceData}
            config={generateChartConfig(
              performanceData,
              ["newCustomers"],
              { newCustomers: "New Customers" },
              { newCustomers: "#F59E0B" }
            )}
            xKey="month"
            yKey="newCustomers"
            height={300}
            tooltip={{ 
              enabled: true,
              formatter: (value) => [`${value} customers`, "New Customers"]
            }}
          />
        </div>
      </section>

      {/* Pie Chart Example */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Pie Chart</h2>
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-medium mb-4">Customer Segmentation</h3>
          <DataVisualization
            type="pie"
            data={customerSegmentData}
            config={generateChartConfig(
              customerSegmentData,
              ["value"],
              { value: "Percentage" }
            )}
            xKey="segment"
            yKey="value"
            height={400}
            tooltip={{ 
              enabled: true,
              formatter: (value, name) => [`${value}%`, name]
            }}
            legend={{ enabled: true, position: "bottom" }}
          />
        </div>
      </section>

      {/* Donut Chart Example */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Donut Chart</h2>
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-medium mb-4">Customer Distribution</h3>
          <DataVisualization
            type="donut"
            data={customerSegmentData}
            config={generateChartConfig(
              customerSegmentData,
              ["customers"],
              { customers: "Customers" }
            )}
            xKey="segment"
            yKey="customers"
            height={400}
            tooltip={{ 
              enabled: true,
              formatter: (value, name) => [`${value.toLocaleString()} customers`, name]
            }}
            legend={{ enabled: true, position: "bottom" }}
          />
        </div>
      </section>

      {/* Multi-line Chart Example */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Multi-line Chart</h2>
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-medium mb-4">Performance Metrics</h3>
          <DataVisualization
            type="line"
            data={performanceData}
            config={generateChartConfig(
              performanceData,
              ["revenue", "churnRate"],
              { revenue: "Revenue ($M)", churnRate: "Churn Rate (%)" },
              { revenue: "#10B981", churnRate: "#EF4444" }
            )}
            xKey="month"
            yKey={["revenue", "churnRate"]}
            height={300}
            tooltip={{ enabled: true }}
            legend={{ enabled: true, position: "top" }}
            axes={{
              yAxis: {
                tickFormatter: (value) => `${value}${typeof value === "number" && value < 10 ? "%" : "M"}`
              }
            }}
          />
        </div>
      </section>

      {/* Custom Renderer Example */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Custom Chart</h2>
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-medium mb-4">Custom SVG Implementation</h3>
          <DataVisualization
            type="custom"
            data={customerSegmentData}
            config={generateChartConfig(customerSegmentData, ["value"])}
            height={200}
            customRenderer={({ data, height }) => (
              <svg width="100%" height={height} className="border rounded">
                <text x="50%" y="50%" textAnchor="middle" className="text-lg font-medium">
                  Custom Chart: {data.length} segments
                </text>
                {data.map((item, index) => (
                  <rect
                    key={item.segment}
                    x={50 + index * 120}
                    y={100}
                    width={100}
                    height={item.value * 2}
                    fill={`hsl(${index * 120}, 70%, 50%)`}
                    className="hover:opacity-80"
                  />
                ))}
              </svg>
            )}
          />
        </div>
      </section>

      {/* Usage Code Examples */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">Usage Examples</h2>
        <div className="bg-gray-50 rounded-lg p-4 space-y-4">
          <h3 className="font-medium">Basic Line Chart</h3>
          <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{`<DataVisualization
  type="line"
  data={data}
  config={generateChartConfig(data, ["revenue"], { revenue: "Revenue" })}
  xKey="period"
  yKey="revenue"
  height={300}
  tooltip={{ enabled: true }}
/>`}
          </pre>

          <h3 className="font-medium">Multi-series Bar Chart</h3>
          <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{`<DataVisualization
  type="bar"
  data={data}
  config={generateChartConfig(data, ["actual", "projected"])}
  xKey="quarter"
  yKey={["actual", "projected"]}
  height={300}
  legend={{ enabled: true, position: "top" }}
/>`}
          </pre>

          <h3 className="font-medium">Pie Chart with Custom Tooltip</h3>
          <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{`<DataVisualization
  type="pie"
  data={segmentData}
  config={generateChartConfig(segmentData, ["value"])}
  xKey="segment"
  yKey="value"
  tooltip={{ 
    formatter: (value, name) => [\`\${value}%\`, name]
  }}
  legend={{ enabled: true }}
/>`}
          </pre>
        </div>
      </section>
    </div>
  )
}