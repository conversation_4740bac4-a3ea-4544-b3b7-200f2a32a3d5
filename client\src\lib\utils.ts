import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Get standardized status color classes
 * @param status - One of: 'positive', 'normal', 'negative'
 * @returns Tailwind CSS classes for the status
 */
export function getStatusClasses(status: string): string {
  switch (status) {
    case 'positive':
      return 'text-green-600';
    case 'normal':
      return 'text-orange-600';
    case 'negative':
      return 'text-red-600';
    default:
      return 'text-gray-900';
  }
}
