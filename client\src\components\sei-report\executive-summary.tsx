import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { CheckCircle, AlertTriangle, Target, ChevronRight, ChevronLeft } from "lucide-react";
import { But<PERSON> } from "@/ui/button";
import { KpiMetric, StrategicInsight, ExecutionPriority } from "@shared/schema";
import { useCompanyData } from "@/hooks/use-company-data";
import { useCompany } from "@/contexts/company-context";
import { getStatusClasses } from "@/lib/utils";

interface ExecutiveSummaryProps {
  onNextSection: () => void;
  onPrevSection: () => void;
}

export function ExecutiveSummary({ onNextSection, onPrevSection }: ExecutiveSummaryProps) {
  const { selectedCompany, companyDisplayName } = useCompany();
  
  const { data: kpis } = useQuery<KpiMetric[]>({
    queryKey: [`/api/companies/${companyDisplayName}/kpis`],
  });

  const { data: insights } = useQuery<StrategicInsight[]>({
    queryKey: [`/api/companies/${companyDisplayName}/insights`],
  });

  const { data: priorities } = useQuery<ExecutionPriority[]>({
    queryKey: [`/api/companies/${companyDisplayName}/priorities`],
  });

  const { data: contentData, loading: contentLoading } = useCompanyData(selectedCompany, "executive-summary");


  const serviceRevenue = kpis?.find(k => k.metricName === "Service Revenue");
  const postpaidAdds = kpis?.find(k => k.metricName === "Postpaid Phone Net Adds");
  const freeCashFlow = kpis?.find(k => k.metricName === "Free Cash Flow");
  const churnRate = kpis?.find(k => k.metricName === "Churn Rate");

  if (contentLoading) {
    return <div className="p-6">Loading...</div>;
  }

  if (!contentData) {
    return <div className="p-6">Failed to load content data</div>;
  }

  return (
    <div className="p-6 space-y-8">
      {/* Summary Analysis Section */}
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-4">Summary Analysis</h2>
        <p className="text-gray-700 mb-6">
          {contentData.summaryAnalysis.text}
        </p>

        {/* Executive Snapshot Cards */}
        <div className="grid grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="text-sm text-gray-500 mb-2">Investability Grade</div>
              <div className={`text-3xl font-bold mb-2 ${getStatusClasses(contentData.executiveSnapshot.investabilityGrade.status)}`}>{contentData.executiveSnapshot.investabilityGrade.value}</div>
              <div className="text-sm text-gray-600">{contentData.executiveSnapshot.investabilityGrade.description}</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-sm text-gray-500 mb-2">Joy Score</div>
              <div className={`text-3xl font-bold mb-2 ${getStatusClasses(contentData.executiveSnapshot.joyScore.status)}`}>{contentData.executiveSnapshot.joyScore.value} <span className="text-sm text-gray-500">/{contentData.executiveSnapshot.joyScore.maxValue}</span></div>
              <div className="text-sm text-gray-600">{contentData.executiveSnapshot.joyScore.description}</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-sm text-gray-500 mb-2">Fog Score</div>
              <div className={`text-3xl font-bold mb-2 ${getStatusClasses(contentData.executiveSnapshot.fogScore.status)}`}>{contentData.executiveSnapshot.fogScore.value} <span className="text-sm text-gray-500">/{contentData.executiveSnapshot.fogScore.maxValue}</span></div>
              <div className="text-sm text-gray-600">{contentData.executiveSnapshot.fogScore.description}</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-sm text-gray-500 mb-2">ROI Potential</div>
              <div className={`text-3xl font-bold mb-2 ${getStatusClasses(contentData.executiveSnapshot.roiPotential.status)}`}>{contentData.executiveSnapshot.roiPotential.value}</div>
              <div className="text-sm text-gray-600">{contentData.executiveSnapshot.roiPotential.description}</div>
            </CardContent>
          </Card>
        </div>

        {/* Key Strengths and Risk Flags */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 mb-4">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <h3 className="font-semibold text-gray-900">Key Strengths</h3>
              </div>
              <ul className="space-y-3">
                {contentData.keyStrengths.map((strength, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                    <span className="text-sm text-gray-700">{strength.text}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 mb-4">
                <AlertTriangle className="w-5 h-5 text-amber-600" />
                <h3 className="font-semibold text-gray-900">Risk Flags</h3>
              </div>
              <ul className="space-y-3">
                {contentData.riskFlags.map((risk, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                    <span className="text-sm text-gray-700">{risk.text}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Strategic Objectives */}
        <Card className="mb-8">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 mb-4">
              <Target className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">Strategic Objectives & Thematic Insights</h3>
            </div>
            <ul className="space-y-3">
              {contentData.strategicObjectives.map((objective, index) => (
                <li key={index} className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-700">{objective.text}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </section>

      {/* Key Performance Indicators */}
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-6">Key Performance Indicators</h2>
        <div className="grid grid-cols-4 gap-6 mb-8">
          {serviceRevenue && (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-green-600 font-medium">+2.3% YoY</span>
                </div>
                <div className="text-sm text-gray-500 mb-1">Service Revenue</div>
                <div className="text-2xl font-bold text-gray-900">${serviceRevenue.value}B</div>
              </CardContent>
            </Card>
          )}
          {postpaidAdds && (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-xs text-green-600 font-medium">****% QoQ</span>
                </div>
                <div className="text-sm text-gray-500 mb-1">Postpaid Phone Net Adds</div>
                <div className="text-2xl font-bold text-gray-900">{postpaidAdds.value}K</div>
              </CardContent>
            </Card>
          )}
          {freeCashFlow && (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-green-600 font-medium">+12% YoY</span>
                </div>
                <div className="text-sm text-gray-500 mb-1">Free Cash Flow</div>
                <div className="text-2xl font-bold text-gray-900">${freeCashFlow.value}B</div>
              </CardContent>
            </Card>
          )}
          {churnRate && (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-xs text-red-600 font-medium">-0.05pp QoQ</span>
                </div>
                <div className="text-sm text-gray-500 mb-1">Churn Rate</div>
                <div className="text-2xl font-bold text-gray-900">{churnRate.value}%</div>
              </CardContent>
            </Card>
          )}
        </div>
      </section>

      {/* Strategic Insights */}
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-4">Strategic Insights</h2>
        <Card className="mb-6">
          <CardContent className="pt-6">
            <ul className="space-y-4">
              {contentData.strategicInsights.map((insight, index) => (
                <li key={index} className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-700">{insight.text}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </section>

      {/* Execution Priorities */}
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-6">Execution Priorities</h2>
        <div className="space-y-4 mb-8">
          {priorities?.map((priority) => (
            <Card key={priority.id}>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-gray-900">{priority.title}</h3>
                  <Badge variant={priority.priority === "HIGH" ? "destructive" : "secondary"}>
                    {priority.priority}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">{priority.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Stakeholder Takeaways */}
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-6">Stakeholder Takeaways</h2>
        <div className="grid grid-cols-2 gap-8">
          <Card>
            <CardContent className="pt-6">
              <h3 className="font-semibold text-gray-900 mb-4">Investor Takeaways</h3>
              <div className="space-y-4">
                {contentData.stakeholderTakeaways.investor.map((takeaway, index) => (
                  <div key={index} className={`border-l-4 pl-4 ${
                    takeaway.priority === 'high' ? 'border-red-400' :
                    takeaway.priority === 'medium' ? 'border-yellow-400' : 
                    'border-blue-400'
                  }`}>
                    <div className={`font-medium text-sm mb-1 ${
                      takeaway.priority === 'high' ? 'text-red-800' :
                      takeaway.priority === 'medium' ? 'text-yellow-800' :
                      'text-blue-800'
                    }`}>
                      {takeaway.priority.charAt(0).toUpperCase() + takeaway.priority.slice(1)}
                    </div>
                    <p className="text-sm text-gray-700">{takeaway.text}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <h3 className="font-semibold text-gray-900 mb-4">Executive Operator Takeaways</h3>
              <div className="space-y-4">
                {contentData.stakeholderTakeaways.executive.map((takeaway, index) => (
                  <div key={index} className={`border-l-4 pl-4 ${
                    takeaway.priority === 'high' ? 'border-red-400' :
                    takeaway.priority === 'medium' ? 'border-yellow-400' : 
                    'border-blue-400'
                  }`}>
                    <div className={`font-medium text-sm mb-1 ${
                      takeaway.priority === 'high' ? 'text-red-800' :
                      takeaway.priority === 'medium' ? 'text-yellow-800' :
                      'text-blue-800'
                    }`}>
                      {takeaway.priority.charAt(0).toUpperCase() + takeaway.priority.slice(1)}
                    </div>
                    <p className="text-sm text-gray-700">{takeaway.text}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-8 border-t border-gray-200">
        <Button variant="ghost" onClick={onPrevSection} className="flex items-center space-x-2">
          <ChevronLeft className="w-4 h-4" />
          <span>Previous Section</span>
        </Button>
        <Button onClick={onNextSection} className="flex items-center space-x-2">
          <span>Next Section</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
