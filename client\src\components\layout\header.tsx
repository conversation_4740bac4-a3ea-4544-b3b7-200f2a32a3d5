import { RefreshCw, Calendar, Bot } from "lucide-react";
import { But<PERSON> } from "@/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/ui/dialog";
import { JoyceChat } from "@/components/joyce-chat";

interface HeaderProps {
  title: string;
  subtitle?: string;
  showRefresh?: boolean;
  showPeriod?: boolean;
  period?: string;
}

export function Header({ 
  title, 
  subtitle, 
  showRefresh = true, 
  showPeriod = true, 
  period = "Q2 2023" 
}: HeaderProps) {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          {subtitle && (
            <p className="text-gray-600 mt-1">{subtitle}</p>
          )}
        </div>
        <div className="flex items-center space-x-4">
          {showRefresh && (
            <Button variant="outline" className="flex items-center space-x-2">
              <RefreshCw className="w-4 h-4" />
              <span>Refresh</span>
            </Button>
          )}
          {showPeriod && (
            <div className="flex items-center text-rejoyce-blue">
              <Calendar className="w-4 h-4 mr-2" />
              <span className="font-medium">{period}</span>
            </div>
          )}
          
          {/* Joyce AI Assistant Modal */}
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="icon" className="relative">
                <Bot className="h-4 w-4 text-blue-600" />
                <span className="sr-only">Open Joyce AI Assistant</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Bot className="h-5 w-5 text-blue-600" />
                  Joyce AI Assistant
                </DialogTitle>
              </DialogHeader>
              <div className="flex-1 mt-4">
                <JoyceChat mode="modal" />
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </header>
  );
}
