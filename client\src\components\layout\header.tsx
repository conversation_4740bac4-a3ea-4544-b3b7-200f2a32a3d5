import { RefreshCw, Calendar } from "lucide-react";
import { Button } from "@/ui/button";

interface HeaderProps {
  title: string;
  subtitle?: string;
  showRefresh?: boolean;
  showPeriod?: boolean;
  period?: string;
}

export function Header({ 
  title, 
  subtitle, 
  showRefresh = true, 
  showPeriod = true, 
  period = "Q2 2023" 
}: HeaderProps) {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          {subtitle && (
            <p className="text-gray-600 mt-1">{subtitle}</p>
          )}
        </div>
        <div className="flex items-center space-x-4">
          {showRefresh && (
            <Button variant="outline" className="flex items-center space-x-2">
              <RefreshCw className="w-4 h-4" />
              <span>Refresh</span>
            </Button>
          )}
          {showPeriod && (
            <div className="flex items-center text-rejoyce-blue">
              <Calendar className="w-4 h-4 mr-2" />
              <span className="font-medium">{period}</span>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
