import { Card, CardContent } from "@/ui/card";
import { TrendingUp, TrendingDown } from "lucide-react";

interface KpiCardProps {
  title: string;
  value: string;
  unit?: string;
  change?: number;
  changeLabel?: string;
  icon?: React.ComponentType<any>;
  color?: "blue" | "green" | "red" | "amber";
}

export function KpiCard({ 
  title, 
  value, 
  unit, 
  change, 
  changeLabel, 
  icon: Icon,
  color = "blue" 
}: KpiCardProps) {
  const colorClasses = {
    blue: "text-blue-600",
    green: "text-green-600", 
    red: "text-red-600",
    amber: "text-amber-600"
  };

  const isPositive = change && change > 0;
  const isNegative = change && change < 0;

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex items-center justify-between mb-2">
          {Icon && <Icon className={`w-5 h-5 ${colorClasses[color]}`} />}
          {change !== undefined && (
            <span className={`text-xs font-medium flex items-center ${
              isPositive ? "text-green-600" : isNegative ? "text-red-600" : "text-gray-600"
            }`}>
              {isPositive && <TrendingUp className="w-3 h-3 mr-1" />}
              {isNegative && <TrendingDown className="w-3 h-3 mr-1" />}
              {change > 0 ? "+" : ""}{change}% {changeLabel}
            </span>
          )}
        </div>
        <div className="text-sm text-gray-500 mb-1">{title}</div>
        <div className="text-2xl font-bold text-gray-900">
          {value}
          {unit && <span className="text-sm font-normal text-gray-600 ml-1">{unit}</span>}
        </div>
      </CardContent>
    </Card>
  );
}
